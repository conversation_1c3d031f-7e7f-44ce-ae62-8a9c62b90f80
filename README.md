# CONVX Portal Journey

A modern data analytics and restaurant management platform built with React, TypeScript, and AWS Amplify.

## 🚀 Quick Start

### Prerequisites

- Node.js 22+ (recommended to use [nvm](https://github.com/nvm-sh/nvm#installing-and-updating))
- pnpm 10.12.3+
- AWS CLI configured (for Amplify backend)

### Setup Instructions

1. **Clone the repository**

   ```sh
   git clone <YOUR_GIT_URL>
   cd convx-portal-journey
   ```

2. **Install dependencies**

   ```sh
   # Install pnpm if you haven't already
   npm install -g pnpm@10.12.3

   # Install project dependencies
   pnpm install
   ```

3. **Set up Amplify backend**

   ```sh
   cd apps/backend
   # Deploy your Amplify backend (this generates amplify_outputs.json)
   npx amplify sandbox
   ```

4. **Copy Amplify configuration**

   ```sh
   # Copy the generated config to the web app
   cp apps/backend/amplify_outputs.json apps/web/src/
   ```

5. **Start development server**
   ```sh
   # From the root directory
   pnpm dev
   ```

The application will be available at `http://localhost:8080`

## 🛠️ Technology Stack

### Frontend

- **React 18** with TypeScript
- **Vite** for build tooling
- **Tailwind CSS** for styling
- **shadcn/ui** component library
- **TanStack React Query** for data fetching
- **React Router** for navigation

### Backend & Authentication

- **AWS Amplify** for backend infrastructure
- **Amazon Cognito** for authentication
- **AWS AppSync** for GraphQL API
- **Supabase** (legacy data layer - being migrated)

### Development Tools

- **pnpm** for package management
- **ESLint** for code linting
- **Prettier** for code formatting
- **Husky** for git hooks
- **Commitlint** for commit message validation

## 📁 Project Structure

```
convx-portal-journey/
├── apps/
│   ├── backend/          # AWS Amplify backend
│   │   ├── amplify/      # Amplify configuration
│   │   └── amplify_outputs.json
│   └── web/              # React frontend application
│       ├── src/
│       ├── public/
│       └── package.json
├── packages/             # Shared packages (if any)
└── package.json          # Root package.json
```

## 🔧 Available Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm lint` - Run ESLint
- `pnpm test` - Run tests
- `pnpm preview` - Preview production build

## 🚀 Deployment

The application is designed to be deployed using AWS Amplify:

1. **Backend**: Deploy using Amplify CLI or Amplify Console
2. **Frontend**: Automatically deployed when connected to your Git repository

## 🔐 Authentication

The application uses AWS Amplify Auth with Amazon Cognito:

- Email-based authentication
- Password requirements: 8+ characters, uppercase, lowercase, numbers, symbols
- Protected routes with automatic redirects

## 📝 Development Notes

- The `amplify_outputs.json` file is gitignored for security
- Each environment should generate its own Amplify configuration
- The project uses a monorepo structure with pnpm workspaces
