# Database Configuration
DATABASE_URL=postgresql+asyncpg://user:password@localhost:5432/convx_db
DATABASE_URL_SYNC=postgresql://user:password@localhost:5432/convx_db

# Test Database
TEST_DATABASE_URL=postgresql+asyncpg://user:password@localhost:5432/convx_test_db

# Application Configuration
APP_NAME=ConvX API
APP_VERSION=0.1.0
DEBUG=true
ENVIRONMENT=development

# Security
SECRET_KEY=your-secret-key-here-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173
ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
ALLOWED_HEADERS=*

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# AWS Amplify Integration
AWS_REGION=us-east-1
AMPLIFY_APP_ID=your-amplify-app-id
COGNITO_USER_POOL_ID=your-user-pool-id
COGNITO_CLIENT_ID=your-client-id

# External Services
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Redis (optional, for caching)
REDIS_URL=redis://localhost:6379/0

# Monitoring
SENTRY_DSN=your-sentry-dsn-here
