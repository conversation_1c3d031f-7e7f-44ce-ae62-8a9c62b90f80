# ConvX API Backend

A Python FastAPI backend service for the ConvX platform.

## Features

- **FastAPI**: Modern, fast web framework for building APIs
- **Async Support**: Full async/await support for high performance
- **SQLAlchemy**: Database ORM with Alembic migrations
- **Authentication**: JWT-based authentication with Amplify integration
- **Testing**: Comprehensive test suite with pytest
- **Code Quality**: Black, isort, mypy, and ruff for code quality
- **Documentation**: Automatic OpenAPI/Swagger documentation

## Project Structure

```
apps/api/
├── src/
│   └── convx_api/
│       ├── __init__.py
│       ├── main.py              # FastAPI application entry point
│       ├── config.py            # Configuration management
│       ├── database.py          # Database connection and setup
│       ├── dependencies.py      # FastAPI dependencies
│       ├── exceptions.py        # Custom exception handlers
│       ├── middleware.py        # Custom middleware
│       ├── models/              # SQLAlchemy models
│       │   ├── __init__.py
│       │   ├── base.py
│       │   └── user.py
│       ├── routers/             # API route handlers
│       │   ├── __init__.py
│       │   ├── auth.py
│       │   ├── health.py
│       │   └── users.py
│       ├── schemas/             # Pydantic schemas
│       │   ├── __init__.py
│       │   ├── auth.py
│       │   └── user.py
│       ├── services/            # Business logic
│       │   ├── __init__.py
│       │   ├── auth.py
│       │   └── user.py
│       └── utils/               # Utility functions
│           ├── __init__.py
│           ├── auth.py
│           ├── database.py
│           └── logging.py
├── tests/                       # Test suite
│   ├── __init__.py
│   ├── conftest.py             # Pytest configuration
│   ├── test_main.py
│   └── unit/
│       ├── __init__.py
│       ├── test_auth.py
│       └── test_users.py
├── alembic/                     # Database migrations
│   ├── versions/
│   ├── env.py
│   └── script.py.mako
├── pyproject.toml              # Python project configuration
├── alembic.ini                 # Alembic configuration
├── Dockerfile                  # Docker configuration
├── docker-compose.yml          # Local development setup
└── .env.example                # Environment variables template
```

## Development Setup

1. **Install Python dependencies**:

   ```bash
   cd apps/api
   poetry install
   ```

2. **Set up environment variables**:

   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Run database migrations**:

   ```bash
   poetry run alembic upgrade head
   ```

4. **Start the development server**:
   ```bash
   poetry run uvicorn src.convx_api.main:app --reload --host 0.0.0.0 --port 8000
   ```

## API Documentation

Once the server is running, you can access:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

## Testing

Run the test suite:

```bash
poetry run pytest
```

Run with coverage:

```bash
poetry run pytest --cov=src/convx_api --cov-report=html
```

## Code Quality

Format code:

```bash
poetry run black src/ tests/
poetry run isort src/ tests/
```

Type checking:

```bash
poetry run mypy src/
```

Linting:

```bash
poetry run ruff check src/ tests/
```

## Docker

Build and run with Docker:

```bash
docker-compose up --build
```
