[tool.poetry]
name = "convx-api"
version = "0.1.0"
description = "ConvX Platform API Backend"
authors = ["ConvX Team <<EMAIL>>"]
readme = "README.md"
packages = [{include = "convx_api", from = "src"}]

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.115.0"
uvicorn = {extras = ["standard"], version = "^0.32.0"}
pydantic = {extras = ["email"], version = "^2.10.0"}
pydantic-settings = "^2.6.0"
sqlalchemy = {extras = ["asyncio"], version = "^2.0.0"}
alembic = "^1.14.0"
asyncpg = "^0.30.0"
psycopg2-binary = "^2.9.0"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.0"}
python-multipart = "^0.0.12"
httpx = "^0.28.0"
redis = "^5.2.0"
celery = "^5.4.0"
structlog = "^24.4.0"
python-json-logger = "^2.0.0"
boto3 = "^1.35.0"
supabase = "^2.9.0"
sentry-sdk = {extras = ["fastapi"], version = "^2.18.0"}

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.0"
pytest-asyncio = "^0.24.0"
pytest-cov = "^6.0.0"
pytest-mock = "^3.14.0"
httpx = "^0.28.0"
black = "^24.10.0"
isort = "^5.13.0"
mypy = "^1.13.0"
ruff = "^0.8.0"
pre-commit = "^4.0.0"
factory-boy = "^3.3.0"
faker = "^33.0.0"

[tool.poetry.group.test.dependencies]
pytest-xdist = "^3.6.0"
pytest-benchmark = "^4.0.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.scripts]
dev = "uvicorn src.convx_api.main:app --reload --host 0.0.0.0 --port 8000"
migrate = "alembic upgrade head"
test = "pytest"

# Black configuration
[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | alembic/versions
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["convx_api"]
known_third_party = ["fastapi", "pydantic", "sqlalchemy", "alembic"]

# mypy configuration
[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "alembic.*",
    "sqlalchemy.*",
    "asyncpg.*",
    "redis.*",
    "celery.*",
    "boto3.*",
    "supabase.*"
]
ignore_missing_imports = true

# Ruff configuration
[tool.ruff]
target-version = "py311"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"tests/**/*" = ["B011"]

# Pytest configuration
[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
asyncio_mode = "auto"

# Coverage configuration
[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/alembic/*",
    "*/__init__.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
