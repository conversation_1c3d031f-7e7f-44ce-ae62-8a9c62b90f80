"""Application configuration management."""

import os
from functools import lru_cache
from typing import Any, Dict, List, Optional

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""

    # Application
    app_name: str = Field(default="ConvX API", env="APP_NAME")
    app_version: str = Field(default="0.1.0", env="APP_VERSION")
    debug: bool = Field(default=False, env="DEBUG")
    environment: str = Field(default="development", env="ENVIRONMENT")

    # Database
    database_url: str = Field(env="DATABASE_URL")
    database_url_sync: str = Field(env="DATABASE_URL_SYNC")
    test_database_url: Optional[str] = Field(default=None, env="TEST_DATABASE_URL")

    # Security
    secret_key: str = Field(env="SECRET_KEY")
    algorithm: str = Field(default="HS256", env="ALGORITHM")
    access_token_expire_minutes: int = Field(
        default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES"
    )

    # CORS
    allowed_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:5173"],
        env="ALLOWED_ORIGINS",
    )
    allowed_methods: List[str] = Field(
        default=["GET", "POST", "PUT", "DELETE", "OPTIONS"], env="ALLOWED_METHODS"
    )
    allowed_headers: List[str] = Field(default=["*"], env="ALLOWED_HEADERS")

    # Logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_format: str = Field(default="json", env="LOG_FORMAT")

    # AWS Amplify
    aws_region: str = Field(default="us-east-1", env="AWS_REGION")
    amplify_app_id: Optional[str] = Field(default=None, env="AMPLIFY_APP_ID")
    cognito_user_pool_id: Optional[str] = Field(
        default=None, env="COGNITO_USER_POOL_ID"
    )
    cognito_client_id: Optional[str] = Field(default=None, env="COGNITO_CLIENT_ID")

    # Supabase
    supabase_url: Optional[str] = Field(default=None, env="SUPABASE_URL")
    supabase_anon_key: Optional[str] = Field(default=None, env="SUPABASE_ANON_KEY")
    supabase_service_role_key: Optional[str] = Field(
        default=None, env="SUPABASE_SERVICE_ROLE_KEY"
    )

    # Redis
    redis_url: Optional[str] = Field(default=None, env="REDIS_URL")

    # Monitoring
    sentry_dsn: Optional[str] = Field(default=None, env="SENTRY_DSN")

    @validator("allowed_origins", pre=True)
    def parse_cors_origins(cls, v: Any) -> List[str]:
        """Parse CORS origins from string or list."""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v

    @validator("allowed_methods", pre=True)
    def parse_cors_methods(cls, v: Any) -> List[str]:
        """Parse CORS methods from string or list."""
        if isinstance(v, str):
            return [method.strip() for method in v.split(",")]
        return v

    @validator("allowed_headers", pre=True)
    def parse_cors_headers(cls, v: Any) -> List[str]:
        """Parse CORS headers from string or list."""
        if isinstance(v, str):
            return [header.strip() for header in v.split(",")]
        return v

    class Config:
        """Pydantic configuration."""

        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

    @property
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment.lower() == "production"

    @property
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.environment.lower() == "development"

    @property
    def is_testing(self) -> bool:
        """Check if running in testing environment."""
        return self.environment.lower() == "testing"

    def get_database_url(self, sync: bool = False) -> str:
        """Get the appropriate database URL."""
        if self.is_testing and self.test_database_url:
            return self.test_database_url
        return self.database_url_sync if sync else self.database_url


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings."""
    return Settings()
