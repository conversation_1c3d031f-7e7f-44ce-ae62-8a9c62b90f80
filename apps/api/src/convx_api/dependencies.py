"""FastAPI dependencies."""

from typing import As<PERSON><PERSON>enerator, Optional

from fastapi import Depends, HTTPException, Request, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from sqlalchemy.ext.asyncio import AsyncSession

from convx_api.config import get_settings
from convx_api.database import get_db

settings = get_settings()
security = HTTPBearer(auto_error=False)


async def get_current_user(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: AsyncSession = Depends(get_db),
) -> Optional[dict]:
    """Get current authenticated user.
    
    This is a placeholder implementation. In a real application,
    you would validate the JWT token and return user information.
    """
    if not credentials:
        return None
    
    # TODO: Implement JWT token validation
    # For now, return a mock user
    return {
        "id": "user_123",
        "email": "<EMAIL>",
        "is_active": True,
    }


async def get_current_active_user(
    current_user: Optional[dict] = Depends(get_current_user),
) -> dict:
    """Get current active user or raise authentication error."""
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not current_user.get("is_active"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user",
        )
    
    return current_user


async def get_admin_user(
    current_user: dict = Depends(get_current_active_user),
) -> dict:
    """Get current admin user or raise authorization error."""
    if not current_user.get("is_admin", False):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions",
        )
    
    return current_user


def get_request_id(request: Request) -> str:
    """Get request ID from request state."""
    return getattr(request.state, "request_id", "unknown")


def get_client_ip(request: Request) -> str:
    """Get client IP address from request."""
    # Check for forwarded headers first
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()
    
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip
    
    # Fallback to direct client IP
    return request.client.host if request.client else "unknown"
