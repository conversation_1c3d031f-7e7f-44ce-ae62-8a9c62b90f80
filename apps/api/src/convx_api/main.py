"""ConvX API FastAPI application."""

import structlog
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from convx_api.config import get_settings
from convx_api.database import create_tables
from convx_api.exceptions import (
    ConvXException,
    convx_exception_handler,
    general_exception_handler,
    http_exception_handler,
)
from convx_api.middleware import (
    RequestLoggingMiddleware,
    SecurityHeadersMiddleware,
    RateLimitMiddleware,
)
from convx_api.routers import health
from convx_api.utils.logging import setup_logging

# Setup logging
setup_logging()
logger = structlog.get_logger()

settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager."""
    logger.info("Starting ConvX API", version=settings.app_version)
    
    # Create database tables
    if settings.is_development:
        await create_tables()
        logger.info("Database tables created")
    
    yield
    
    logger.info("Shutting down ConvX API")


# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="ConvX Platform API Backend",
    docs_url="/docs" if not settings.is_production else None,
    redoc_url="/redoc" if not settings.is_production else None,
    openapi_url="/openapi.json" if not settings.is_production else None,
    lifespan=lifespan,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=settings.allowed_methods,
    allow_headers=settings.allowed_headers,
)

# Add custom middleware
app.add_middleware(RequestLoggingMiddleware)
app.add_middleware(SecurityHeadersMiddleware)

# Add rate limiting in production
if settings.is_production:
    app.add_middleware(RateLimitMiddleware, calls=100, period=60)

# Add exception handlers
app.add_exception_handler(ConvXException, convx_exception_handler)
app.add_exception_handler(HTTPException, http_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)

# Include routers
app.include_router(health.router)


@app.get("/", tags=["root"])
async def root() -> dict:
    """Root endpoint."""
    return {
        "message": "Welcome to ConvX API",
        "version": settings.app_version,
        "docs": "/docs" if not settings.is_production else "Documentation disabled in production",
    }


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "convx_api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
    )
