"""Authentication endpoints."""

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from convx_api.database import get_db
from convx_api.dependencies import get_current_active_user, get_current_user
from convx_api.exceptions import AuthenticationException, ConflictException
from convx_api.schemas.auth import (
    AuthUser,
    CognitoTokenRequest,
    LoginRequest,
    RegisterRequest,
    SupabaseTokenRequest,
    TokenResponse,
    UserPermissions,
)
from convx_api.services.auth import AuthService
from convx_api.utils.auth import validate_cognito_token, validate_supabase_token

router = APIRouter(prefix="/auth", tags=["authentication"])


@router.post(
    "/register",
    response_model=TokenResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Register new user",
    description="Register a new user with email and password",
)
async def register(
    request: RegisterRequest,
    db: AsyncSession = Depends(get_db),
) -> TokenResponse:
    """Register a new user."""
    auth_service = AuthService(db)
    
    try:
        return await auth_service.register(request)
    except ConflictException as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e),
        )


@router.post(
    "/login",
    response_model=TokenResponse,
    status_code=status.HTTP_200_OK,
    summary="User login",
    description="Authenticate user with email and password",
)
async def login(
    request: LoginRequest,
    db: AsyncSession = Depends(get_db),
) -> TokenResponse:
    """Authenticate user and return access token."""
    auth_service = AuthService(db)
    
    try:
        return await auth_service.login(request)
    except AuthenticationException as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )


@router.post(
    "/cognito",
    response_model=TokenResponse,
    status_code=status.HTTP_200_OK,
    summary="Cognito token exchange",
    description="Exchange Cognito token for API access token",
)
async def cognito_auth(
    request: CognitoTokenRequest,
    db: AsyncSession = Depends(get_db),
) -> TokenResponse:
    """Exchange Cognito token for API access token."""
    # Validate Cognito token
    payload = validate_cognito_token(request.access_token)
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid Cognito token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Extract user information from token
    email = payload.get("email")
    cognito_user_id = payload.get("sub")
    first_name = payload.get("given_name")
    last_name = payload.get("family_name")

    if not email or not cognito_user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token payload",
        )

    auth_service = AuthService(db)
    
    # Create or get user
    user = await auth_service.create_user_from_external(
        email=email,
        external_id=cognito_user_id,
        provider="cognito",
        first_name=first_name,
        last_name=last_name,
    )

    # Create API access token
    from convx_api.utils.auth import create_access_token, create_token_data
    from datetime import timedelta
    from convx_api.config import get_settings
    
    settings = get_settings()
    token_data = create_token_data(
        user_id=user.id,
        email=user.email,
        scopes=auth_service._get_user_scopes(user),
    )

    access_token = create_access_token(
        data=token_data,
        expires_delta=timedelta(minutes=settings.access_token_expire_minutes),
    )

    return TokenResponse(
        access_token=access_token,
        token_type="bearer",
        expires_in=settings.access_token_expire_minutes * 60,
        user_id=user.id,
        email=user.email,
    )


@router.post(
    "/supabase",
    response_model=TokenResponse,
    status_code=status.HTTP_200_OK,
    summary="Supabase token exchange",
    description="Exchange Supabase token for API access token",
)
async def supabase_auth(
    request: SupabaseTokenRequest,
    db: AsyncSession = Depends(get_db),
) -> TokenResponse:
    """Exchange Supabase token for API access token."""
    # Validate Supabase token
    payload = validate_supabase_token(request.access_token)
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid Supabase token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Extract user information from token
    email = payload.get("email")
    supabase_user_id = payload.get("sub")
    first_name = payload.get("user_metadata", {}).get("first_name")
    last_name = payload.get("user_metadata", {}).get("last_name")

    if not email or not supabase_user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token payload",
        )

    auth_service = AuthService(db)
    
    # Create or get user
    user = await auth_service.create_user_from_external(
        email=email,
        external_id=supabase_user_id,
        provider="supabase",
        first_name=first_name,
        last_name=last_name,
    )

    # Create API access token
    from convx_api.utils.auth import create_access_token, create_token_data
    from datetime import timedelta
    from convx_api.config import get_settings
    
    settings = get_settings()
    token_data = create_token_data(
        user_id=user.id,
        email=user.email,
        scopes=auth_service._get_user_scopes(user),
    )

    access_token = create_access_token(
        data=token_data,
        expires_delta=timedelta(minutes=settings.access_token_expire_minutes),
    )

    return TokenResponse(
        access_token=access_token,
        token_type="bearer",
        expires_in=settings.access_token_expire_minutes * 60,
        user_id=user.id,
        email=user.email,
    )


@router.get(
    "/me",
    response_model=AuthUser,
    status_code=status.HTTP_200_OK,
    summary="Get current user",
    description="Get current authenticated user information",
)
async def get_current_user_info(
    current_user: dict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> AuthUser:
    """Get current authenticated user information."""
    auth_service = AuthService(db)
    user = await auth_service.get_user_by_id(current_user["id"])
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found",
        )

    return AuthUser(
        id=user.id,
        email=user.email,
        first_name=user.first_name,
        last_name=user.last_name,
        permissions=UserPermissions(
            scopes=auth_service._get_user_scopes(user),
            is_admin=user.is_admin,
            is_tutor=user.is_tutor,
            is_active=user.is_active,
            is_verified=user.is_verified,
        ),
    )


@router.post(
    "/logout",
    status_code=status.HTTP_200_OK,
    summary="User logout",
    description="Logout current user (client-side token removal)",
)
async def logout(
    current_user: dict = Depends(get_current_user),
) -> dict:
    """Logout user (client should remove token)."""
    return {"message": "Successfully logged out"}
