"""Health check endpoints."""

import time
from typing import Dict, Any

from fastapi import APIRouter, Depends, status
from pydantic import BaseModel
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from convx_api.config import get_settings
from convx_api.database import get_db

router = APIRouter(prefix="/health", tags=["health"])
settings = get_settings()


class HealthResponse(BaseModel):
    """Health check response model."""

    status: str
    timestamp: float
    version: str
    environment: str
    checks: Dict[str, Any]


class SimpleHealthResponse(BaseModel):
    """Simple health check response."""

    status: str
    timestamp: float


@router.get(
    "/",
    response_model=SimpleHealthResponse,
    status_code=status.HTTP_200_OK,
    summary="Simple health check",
    description="Returns basic health status of the API",
)
async def health_check() -> SimpleHealthResponse:
    """Simple health check endpoint."""
    return SimpleHealthResponse(
        status="healthy",
        timestamp=time.time(),
    )


@router.get(
    "/detailed",
    response_model=HealthResponse,
    status_code=status.HTTP_200_OK,
    summary="Detailed health check",
    description="Returns detailed health status including database connectivity",
)
async def detailed_health_check(
    db: AsyncSession = Depends(get_db),
) -> HealthResponse:
    """Detailed health check with database connectivity."""
    checks = {}
    overall_status = "healthy"

    # Database check
    try:
        result = await db.execute(text("SELECT 1"))
        db_status = "healthy" if result.scalar() == 1 else "unhealthy"
        checks["database"] = {
            "status": db_status,
            "message": "Database connection successful",
        }
    except Exception as e:
        checks["database"] = {
            "status": "unhealthy",
            "message": f"Database connection failed: {str(e)}",
        }
        overall_status = "unhealthy"

    # Redis check (if configured)
    if settings.redis_url:
        try:
            # TODO: Add Redis connectivity check
            checks["redis"] = {
                "status": "healthy",
                "message": "Redis connection successful",
            }
        except Exception as e:
            checks["redis"] = {
                "status": "unhealthy",
                "message": f"Redis connection failed: {str(e)}",
            }
            overall_status = "degraded"

    # External services check
    checks["external_services"] = {
        "supabase": {
            "configured": bool(settings.supabase_url),
            "status": "unknown",
        },
        "amplify": {
            "configured": bool(settings.cognito_user_pool_id),
            "status": "unknown",
        },
    }

    return HealthResponse(
        status=overall_status,
        timestamp=time.time(),
        version=settings.app_version,
        environment=settings.environment,
        checks=checks,
    )


@router.get(
    "/readiness",
    status_code=status.HTTP_200_OK,
    summary="Readiness probe",
    description="Kubernetes readiness probe endpoint",
)
async def readiness_check(db: AsyncSession = Depends(get_db)) -> Dict[str, str]:
    """Readiness probe for Kubernetes."""
    try:
        # Check database connectivity
        await db.execute(text("SELECT 1"))
        return {"status": "ready"}
    except Exception:
        return {"status": "not ready"}


@router.get(
    "/liveness",
    status_code=status.HTTP_200_OK,
    summary="Liveness probe",
    description="Kubernetes liveness probe endpoint",
)
async def liveness_check() -> Dict[str, str]:
    """Liveness probe for Kubernetes."""
    return {"status": "alive"}
