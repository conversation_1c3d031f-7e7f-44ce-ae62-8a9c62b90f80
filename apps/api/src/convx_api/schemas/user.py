"""User schemas."""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, EmailStr, Field, HttpUrl


class UserBase(BaseModel):
    """Base user schema."""

    email: EmailStr
    first_name: Optional[str] = Field(None, max_length=100)
    last_name: Optional[str] = Field(None, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    bio: Optional[str] = Field(None, max_length=1000)
    avatar_url: Optional[HttpUrl] = None


class UserCreate(UserBase):
    """User creation schema."""

    password: Optional[str] = Field(None, min_length=8, max_length=128)
    is_admin: bool = False
    is_tutor: bool = False
    cognito_user_id: Optional[str] = None
    supabase_user_id: Optional[str] = None


class UserUpdate(BaseModel):
    """User update schema."""

    first_name: Optional[str] = Field(None, max_length=100)
    last_name: Optional[str] = Field(None, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    bio: Optional[str] = Field(None, max_length=1000)
    avatar_url: Optional[HttpUrl] = None


class UserResponse(UserBase):
    """User response schema."""

    id: str
    is_active: bool
    is_verified: bool
    is_admin: bool
    is_tutor: bool
    created_at: datetime
    updated_at: datetime
    full_name: str

    class Config:
        """Pydantic configuration."""

        from_attributes = True


class UserListResponse(BaseModel):
    """User list response schema."""

    users: list[UserResponse]
    total: int
    page: int
    per_page: int
    pages: int


class UserProfile(BaseModel):
    """User profile schema."""

    id: str
    email: EmailStr
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone: Optional[str] = None
    bio: Optional[str] = None
    avatar_url: Optional[HttpUrl] = None
    is_verified: bool
    is_tutor: bool
    created_at: datetime
    full_name: str

    class Config:
        """Pydantic configuration."""

        from_attributes = True


class AdminUserResponse(UserResponse):
    """Admin user response schema with sensitive fields."""

    cognito_user_id: Optional[str] = None
    supabase_user_id: Optional[str] = None

    class Config:
        """Pydantic configuration."""

        from_attributes = True
