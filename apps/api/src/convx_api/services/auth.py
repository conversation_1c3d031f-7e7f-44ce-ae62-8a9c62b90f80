"""Authentication service."""

from datetime import <PERSON><PERSON><PERSON>
from typing import <PERSON><PERSON>, <PERSON><PERSON>

from sqlalchemy.ext.asyncio import AsyncSession

from convx_api.config import get_settings
from convx_api.exceptions import AuthenticationException, ConflictException, NotFoundException
from convx_api.models.user import User
from convx_api.schemas.auth import LoginRequest, RegisterRequest, TokenResponse
from convx_api.utils.auth import (
    create_access_token,
    create_token_data,
    hash_password,
    verify_password,
)
from convx_api.utils.database import DatabaseManager

settings = get_settings()


class AuthService:
    """Authentication service."""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.db_manager = DatabaseManager(db)

    async def register(self, request: RegisterRequest) -> TokenResponse:
        """Register a new user."""
        # Check if user already exists
        existing_user = await self.db_manager.get_by_field(
            User, "email", request.email
        )
        if existing_user:
            raise ConflictException("User with this email already exists")

        # Create new user
        user_data = {
            "email": request.email,
            "hashed_password": hash_password(request.password),
            "first_name": request.first_name,
            "last_name": request.last_name,
            "phone": request.phone,
            "is_active": True,
            "is_verified": False,
        }

        user = await self.db_manager.create(User, **user_data)

        # Create access token
        token_data = create_token_data(
            user_id=user.id,
            email=user.email,
            scopes=self._get_user_scopes(user),
        )

        access_token = create_access_token(
            data=token_data,
            expires_delta=timedelta(minutes=settings.access_token_expire_minutes),
        )

        return TokenResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.access_token_expire_minutes * 60,
            user_id=user.id,
            email=user.email,
        )

    async def login(self, request: LoginRequest) -> TokenResponse:
        """Authenticate user and return token."""
        # Get user by email
        user = await self.db_manager.get_by_field(User, "email", request.email)
        if not user:
            raise AuthenticationException("Invalid email or password")

        # Verify password
        if not user.hashed_password or not verify_password(
            request.password, user.hashed_password
        ):
            raise AuthenticationException("Invalid email or password")

        # Check if user is active
        if not user.is_active:
            raise AuthenticationException("Account is deactivated")

        # Create access token
        token_data = create_token_data(
            user_id=user.id,
            email=user.email,
            scopes=self._get_user_scopes(user),
        )

        access_token = create_access_token(
            data=token_data,
            expires_delta=timedelta(minutes=settings.access_token_expire_minutes),
        )

        return TokenResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.access_token_expire_minutes * 60,
            user_id=user.id,
            email=user.email,
        )

    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get user by ID."""
        return await self.db_manager.get_by_id(User, user_id)

    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        return await self.db_manager.get_by_field(User, "email", email)

    async def create_user_from_external(
        self,
        email: str,
        external_id: str,
        provider: str,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
    ) -> User:
        """Create user from external authentication provider."""
        # Check if user already exists
        existing_user = await self.get_user_by_email(email)
        if existing_user:
            # Update external ID if not set
            update_data = {}
            if provider == "cognito" and not existing_user.cognito_user_id:
                update_data["cognito_user_id"] = external_id
            elif provider == "supabase" and not existing_user.supabase_user_id:
                update_data["supabase_user_id"] = external_id

            if update_data:
                await self.db_manager.update(existing_user, **update_data)

            return existing_user

        # Create new user
        user_data = {
            "email": email,
            "first_name": first_name,
            "last_name": last_name,
            "is_active": True,
            "is_verified": True,  # External providers handle verification
        }

        if provider == "cognito":
            user_data["cognito_user_id"] = external_id
        elif provider == "supabase":
            user_data["supabase_user_id"] = external_id

        return await self.db_manager.create(User, **user_data)

    async def verify_email(self, user_id: str) -> User:
        """Mark user email as verified."""
        user = await self.get_user_by_id(user_id)
        if not user:
            raise NotFoundException("User not found")

        return await self.db_manager.update(user, is_verified=True)

    async def deactivate_user(self, user_id: str) -> User:
        """Deactivate user account."""
        user = await self.get_user_by_id(user_id)
        if not user:
            raise NotFoundException("User not found")

        return await self.db_manager.update(user, is_active=False)

    async def activate_user(self, user_id: str) -> User:
        """Activate user account."""
        user = await self.get_user_by_id(user_id)
        if not user:
            raise NotFoundException("User not found")

        return await self.db_manager.update(user, is_active=True)

    def _get_user_scopes(self, user: User) -> list[str]:
        """Get user scopes based on roles."""
        scopes = ["user:read"]

        if user.is_admin:
            scopes.extend([
                "admin:read",
                "admin:write",
                "user:write",
                "user:delete",
            ])

        if user.is_tutor:
            scopes.extend([
                "tutor:read",
                "tutor:write",
            ])

        return scopes
