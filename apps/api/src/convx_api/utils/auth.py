"""Authentication and authorization utilities."""

import secrets
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Union

import jwt
from passlib.context import Crypt<PERSON>ontext
from passlib.hash import bcrypt

from convx_api.config import get_settings

settings = get_settings()

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def create_access_token(
    data: Dict[str, Any],
    expires_delta: Optional[timedelta] = None,
) -> str:
    """Create JWT access token."""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.access_token_expire_minutes
        )
    
    to_encode.update({"exp": expire})
    
    encoded_jwt = jwt.encode(
        to_encode,
        settings.secret_key,
        algorithm=settings.algorithm,
    )
    
    return encoded_jwt


def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """Verify and decode JWT token."""
    try:
        payload = jwt.decode(
            token,
            settings.secret_key,
            algorithms=[settings.algorithm],
        )
        return payload
    except jwt.PyJWTError:
        return None


def hash_password(password: str) -> str:
    """Hash a password using bcrypt."""
    return pwd_context.hash(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash."""
    return pwd_context.verify(plain_password, hashed_password)


def generate_password_reset_token() -> str:
    """Generate a secure password reset token."""
    return secrets.token_urlsafe(32)


def generate_verification_token() -> str:
    """Generate a secure email verification token."""
    return secrets.token_urlsafe(32)


class TokenData:
    """Token data structure."""

    def __init__(self, user_id: str, email: str, scopes: Optional[list] = None):
        self.user_id = user_id
        self.email = email
        self.scopes = scopes or []


def create_token_data(user_id: str, email: str, scopes: Optional[list] = None) -> Dict[str, Any]:
    """Create token data dictionary."""
    return {
        "sub": user_id,
        "email": email,
        "scopes": scopes or [],
        "type": "access_token",
    }


def extract_token_data(payload: Dict[str, Any]) -> Optional[TokenData]:
    """Extract token data from JWT payload."""
    try:
        user_id = payload.get("sub")
        email = payload.get("email")
        scopes = payload.get("scopes", [])
        
        if not user_id or not email:
            return None
        
        return TokenData(user_id=user_id, email=email, scopes=scopes)
    except Exception:
        return None


def validate_cognito_token(token: str) -> Optional[Dict[str, Any]]:
    """Validate AWS Cognito JWT token.
    
    This is a placeholder implementation. In a real application,
    you would validate the token against Cognito's public keys.
    """
    # TODO: Implement Cognito token validation
    # 1. Get Cognito public keys from JWKS endpoint
    # 2. Verify token signature
    # 3. Validate token claims (iss, aud, exp, etc.)
    
    try:
        # For now, just decode without verification (NOT for production)
        payload = jwt.decode(token, options={"verify_signature": False})
        return payload
    except jwt.PyJWTError:
        return None


def validate_supabase_token(token: str) -> Optional[Dict[str, Any]]:
    """Validate Supabase JWT token.
    
    This is a placeholder implementation. In a real application,
    you would validate the token against Supabase's public keys.
    """
    # TODO: Implement Supabase token validation
    # 1. Get Supabase public keys
    # 2. Verify token signature
    # 3. Validate token claims
    
    try:
        # For now, just decode without verification (NOT for production)
        payload = jwt.decode(token, options={"verify_signature": False})
        return payload
    except jwt.PyJWTError:
        return None


def check_permissions(user_scopes: list, required_scopes: list) -> bool:
    """Check if user has required permissions."""
    return all(scope in user_scopes for scope in required_scopes)


def is_admin(user_data: Dict[str, Any]) -> bool:
    """Check if user has admin privileges."""
    return user_data.get("is_admin", False)


def is_tutor(user_data: Dict[str, Any]) -> bool:
    """Check if user is a tutor."""
    return user_data.get("is_tutor", False)


def is_active_user(user_data: Dict[str, Any]) -> bool:
    """Check if user account is active."""
    return user_data.get("is_active", False)


def is_verified_user(user_data: Dict[str, Any]) -> bool:
    """Check if user email is verified."""
    return user_data.get("is_verified", False)
