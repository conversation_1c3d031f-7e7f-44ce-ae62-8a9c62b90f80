"""Database utility functions."""

from typing import Any, Dict, List, Optional, Type, TypeVar
from uuid import uuid4

from sqlalchemy import select, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from convx_api.models.base import BaseModel

ModelType = TypeVar("ModelType", bound=BaseModel)


class DatabaseManager:
    """Database operations manager."""

    def __init__(self, session: AsyncSession):
        self.session = session

    async def create(self, model_class: Type[ModelType], **kwargs: Any) -> ModelType:
        """Create a new model instance."""
        instance = model_class(**kwargs)
        self.session.add(instance)
        await self.session.commit()
        await self.session.refresh(instance)
        return instance

    async def get_by_id(
        self,
        model_class: Type[ModelType],
        id: str,
        load_relationships: Optional[List[str]] = None,
    ) -> Optional[ModelType]:
        """Get model instance by ID."""
        query = select(model_class).where(model_class.id == id)
        
        if load_relationships:
            for relationship in load_relationships:
                query = query.options(selectinload(getattr(model_class, relationship)))
        
        result = await self.session.execute(query)
        return result.scalar_one_or_none()

    async def get_by_field(
        self,
        model_class: Type[ModelType],
        field_name: str,
        field_value: Any,
        load_relationships: Optional[List[str]] = None,
    ) -> Optional[ModelType]:
        """Get model instance by field value."""
        query = select(model_class).where(getattr(model_class, field_name) == field_value)
        
        if load_relationships:
            for relationship in load_relationships:
                query = query.options(selectinload(getattr(model_class, relationship)))
        
        result = await self.session.execute(query)
        return result.scalar_one_or_none()

    async def get_all(
        self,
        model_class: Type[ModelType],
        filters: Optional[Dict[str, Any]] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        order_by: Optional[str] = None,
        load_relationships: Optional[List[str]] = None,
    ) -> List[ModelType]:
        """Get all model instances with optional filtering and pagination."""
        query = select(model_class)
        
        # Apply filters
        if filters:
            conditions = []
            for field_name, field_value in filters.items():
                if hasattr(model_class, field_name):
                    conditions.append(getattr(model_class, field_name) == field_value)
            if conditions:
                query = query.where(and_(*conditions))
        
        # Apply ordering
        if order_by:
            if order_by.startswith("-"):
                field_name = order_by[1:]
                if hasattr(model_class, field_name):
                    query = query.order_by(getattr(model_class, field_name).desc())
            else:
                if hasattr(model_class, order_by):
                    query = query.order_by(getattr(model_class, order_by))
        
        # Apply pagination
        if offset:
            query = query.offset(offset)
        if limit:
            query = query.limit(limit)
        
        # Load relationships
        if load_relationships:
            for relationship in load_relationships:
                query = query.options(selectinload(getattr(model_class, relationship)))
        
        result = await self.session.execute(query)
        return result.scalars().all()

    async def update(
        self,
        instance: ModelType,
        **kwargs: Any,
    ) -> ModelType:
        """Update model instance."""
        for field_name, field_value in kwargs.items():
            if hasattr(instance, field_name):
                setattr(instance, field_name, field_value)
        
        await self.session.commit()
        await self.session.refresh(instance)
        return instance

    async def delete(self, instance: ModelType) -> None:
        """Delete model instance."""
        await self.session.delete(instance)
        await self.session.commit()

    async def count(
        self,
        model_class: Type[ModelType],
        filters: Optional[Dict[str, Any]] = None,
    ) -> int:
        """Count model instances with optional filtering."""
        query = select(func.count(model_class.id))
        
        # Apply filters
        if filters:
            conditions = []
            for field_name, field_value in filters.items():
                if hasattr(model_class, field_name):
                    conditions.append(getattr(model_class, field_name) == field_value)
            if conditions:
                query = query.where(and_(*conditions))
        
        result = await self.session.execute(query)
        return result.scalar()

    async def exists(
        self,
        model_class: Type[ModelType],
        filters: Dict[str, Any],
    ) -> bool:
        """Check if model instance exists with given filters."""
        count = await self.count(model_class, filters)
        return count > 0

    async def bulk_create(
        self,
        model_class: Type[ModelType],
        data_list: List[Dict[str, Any]],
    ) -> List[ModelType]:
        """Create multiple model instances."""
        instances = []
        for data in data_list:
            instance = model_class(**data)
            instances.append(instance)
            self.session.add(instance)
        
        await self.session.commit()
        
        # Refresh all instances
        for instance in instances:
            await self.session.refresh(instance)
        
        return instances


def generate_uuid() -> str:
    """Generate a new UUID string."""
    return str(uuid4())


async def get_or_create(
    session: AsyncSession,
    model_class: Type[ModelType],
    defaults: Optional[Dict[str, Any]] = None,
    **kwargs: Any,
) -> tuple[ModelType, bool]:
    """Get existing instance or create new one."""
    db_manager = DatabaseManager(session)
    
    # Try to find existing instance
    conditions = []
    for field_name, field_value in kwargs.items():
        if hasattr(model_class, field_name):
            conditions.append(getattr(model_class, field_name) == field_value)
    
    if conditions:
        query = select(model_class).where(and_(*conditions))
        result = await session.execute(query)
        instance = result.scalar_one_or_none()
        
        if instance:
            return instance, False
    
    # Create new instance
    create_data = {**kwargs}
    if defaults:
        create_data.update(defaults)
    
    instance = await db_manager.create(model_class, **create_data)
    return instance, True
