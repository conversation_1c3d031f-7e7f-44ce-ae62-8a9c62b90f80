"""Logging configuration and utilities."""

import logging
import sys
from typing import Any, Dict

import structlog
from pythonjsonlogger import jsonlogger

from convx_api.config import get_settings

settings = get_settings()


def setup_logging() -> None:
    """Configure structured logging."""
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, settings.log_level.upper()),
    )

    # Configure structlog
    processors = [
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
    ]

    if settings.log_format == "json":
        processors.append(structlog.processors.JSONRenderer())
    else:
        processors.append(structlog.dev.ConsoleRenderer())

    structlog.configure(
        processors=processors,
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )

    # Configure uvicorn logging
    if settings.log_format == "json":
        formatter = jsonlogger.JsonFormatter(
            "%(asctime)s %(name)s %(levelname)s %(message)s"
        )
        handler = logging.StreamHandler()
        handler.setFormatter(formatter)
        
        # Update uvicorn loggers
        for logger_name in ["uvicorn", "uvicorn.access", "uvicorn.error"]:
            logger = logging.getLogger(logger_name)
            logger.handlers = [handler]


def get_logger(name: str = None) -> structlog.BoundLogger:
    """Get a structured logger instance."""
    return structlog.get_logger(name)


def log_request_response(
    method: str,
    url: str,
    status_code: int,
    duration: float,
    request_id: str,
    **kwargs: Any,
) -> None:
    """Log HTTP request/response details."""
    logger = get_logger("http")
    
    log_data = {
        "method": method,
        "url": url,
        "status_code": status_code,
        "duration": duration,
        "request_id": request_id,
        **kwargs,
    }
    
    if status_code >= 500:
        logger.error("HTTP request failed", **log_data)
    elif status_code >= 400:
        logger.warning("HTTP request error", **log_data)
    else:
        logger.info("HTTP request completed", **log_data)


def log_database_operation(
    operation: str,
    table: str,
    duration: float,
    success: bool = True,
    **kwargs: Any,
) -> None:
    """Log database operation details."""
    logger = get_logger("database")
    
    log_data = {
        "operation": operation,
        "table": table,
        "duration": duration,
        "success": success,
        **kwargs,
    }
    
    if success:
        logger.info("Database operation completed", **log_data)
    else:
        logger.error("Database operation failed", **log_data)


def log_auth_event(
    event: str,
    user_id: str = None,
    success: bool = True,
    **kwargs: Any,
) -> None:
    """Log authentication/authorization events."""
    logger = get_logger("auth")
    
    log_data = {
        "event": event,
        "user_id": user_id,
        "success": success,
        **kwargs,
    }
    
    if success:
        logger.info("Auth event", **log_data)
    else:
        logger.warning("Auth event failed", **log_data)
