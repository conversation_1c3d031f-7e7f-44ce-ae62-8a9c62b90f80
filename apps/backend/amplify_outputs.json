{"auth": {"user_pool_id": "us-east-1_WJBy0cMnd", "aws_region": "us-east-1", "user_pool_client_id": "7iadc2ljnahlegfth824e3p3mh", "identity_pool_id": "us-east-1:99d74c7e-006b-4df0-8519-e925eacff0ef", "mfa_methods": [], "standard_required_attributes": ["email"], "username_attributes": ["email"], "user_verification_types": ["email"], "groups": [], "mfa_configuration": "NONE", "password_policy": {"min_length": 8, "require_lowercase": true, "require_numbers": true, "require_symbols": true, "require_uppercase": true}, "unauthenticated_identities_enabled": true}, "data": {"url": "https://3oh5vyu3lvbjvc74ax6zedkz3i.appsync-api.us-east-1.amazonaws.com/graphql", "aws_region": "us-east-1", "default_authorization_type": "AWS_IAM", "authorization_types": ["AMAZON_COGNITO_USER_POOLS"], "model_introspection": {"version": 1, "models": {"Todo": {"name": "Todo", "fields": {"id": {"name": "id", "isArray": false, "type": "ID", "isRequired": true, "attributes": []}, "content": {"name": "content", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}}, "syncable": true, "pluralName": "Todos", "attributes": [{"type": "model", "properties": {}}, {"type": "auth", "properties": {"rules": [{"allow": "private", "operations": ["create", "update", "delete", "read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": false, "primaryKeyFieldName": "id", "sortKeyFieldNames": []}}}, "enums": {}, "nonModels": {}}}, "version": "1.4"}