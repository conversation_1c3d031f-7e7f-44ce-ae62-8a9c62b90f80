# API Integration Guide

## Current State

The application currently operates with static sample data for demonstration purposes. This guide outlines the architecture and patterns designed for future real-time data integration.

## Integration Architecture

### Data Flow Design

```
External APIs → Data Layer → React Query → Components → UI
     ↓              ↓           ↓           ↓        ↓
  POS Systems   Transformers  Cache    State Mgmt  Display
  Inventory     Validators    Queries   Props      Charts
  Reviews       Normalizers   Mutations Hooks     Metrics
```

### Planned Integration Points

#### 1. Restaurant POS Systems

**Purpose**: Real-time sales and transaction data
**Data Types**:

- Daily sales figures
- Transaction details
- Product performance
- Customer counts

**Sample Integration**:

```typescript
// Future API service
interface POSService {
  getSalesData(dateRange: DateRange, locationId?: string): Promise<SalesData[]>;
  getDailySales(date: string): Promise<DailySalesData>;
  getTransactionDetails(transactionId: string): Promise<Transaction>;
}

// React Query implementation
const useSalesData = (dateRange: DateRange) => {
  return useQuery({
    queryKey: ['sales', dateRange],
    queryFn: () => posService.getSalesData(dateRange),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 30 * 1000, // 30 seconds
  });
};
```

#### 2. Inventory Management Systems

**Purpose**: Cost analysis and inventory tracking
**Data Types**:

- Food cost percentages
- Inventory levels
- Waste tracking
- Supplier costs

#### 3. Staff Management Systems

**Purpose**: Labor cost analysis and scheduling
**Data Types**:

- Labor cost percentages
- Staff scheduling
- Productivity metrics
- Training records

#### 4. Customer Review Platforms

**Purpose**: Sentiment analysis and customer feedback
**Data Sources**:

- Google Reviews
- Yelp Reviews
- Internal feedback systems
- Social media mentions

**Sample Integration**:

```typescript
interface ReviewService {
  getReviews(locationId: string, limit?: number): Promise<Review[]>;
  getSentimentAnalysis(locationId: string): Promise<SentimentData>;
  getReviewTrends(dateRange: DateRange): Promise<ReviewTrend[]>;
}

interface Review {
  id: string;
  rating: number;
  text: string;
  date: string;
  source: 'google' | 'yelp' | 'internal';
  sentiment: 'positive' | 'neutral' | 'negative';
  location: string;
}
```

#### 5. External Data Sources

**Purpose**: Environmental factors affecting performance
**Data Types**:

- Weather data
- Local events
- Traffic patterns
- Competitor analysis

## Implementation Patterns

### 1. Service Layer Architecture

```typescript
// Base API service
abstract class BaseAPIService {
  protected baseURL: string;
  protected headers: Record<string, string>;

  constructor(baseURL: string, apiKey?: string) {
    this.baseURL = baseURL;
    this.headers = {
      'Content-Type': 'application/json',
      ...(apiKey && { Authorization: `Bearer ${apiKey}` }),
    };
  }

  protected async request<T>(
    endpoint: string,
    options?: RequestInit
  ): Promise<T> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      headers: this.headers,
      ...options,
    });

    if (!response.ok) {
      throw new APIError(response.status, response.statusText);
    }

    return response.json();
  }
}

// Specific service implementation
class SalesService extends BaseAPIService {
  async getSalesData(params: SalesParams): Promise<SalesData[]> {
    return this.request('/sales', {
      method: 'GET',
      // Add query parameters
    });
  }
}
```

### 2. Data Transformation Layer

```typescript
// Raw API data transformation
interface RawSalesData {
  transaction_date: string;
  amount_cents: number;
  location_code: string;
  // Raw API format
}

interface TransformedSalesData {
  date: string;
  amount: number;
  location: string;
  // Application format
}

const transformSalesData = (raw: RawSalesData[]): TransformedSalesData[] => {
  return raw.map(item => ({
    date: new Date(item.transaction_date).toISOString().split('T')[0],
    amount: item.amount_cents / 100,
    location: item.location_code,
  }));
};
```

### 3. Error Handling Strategy

```typescript
class APIError extends Error {
  constructor(
    public status: number,
    public message: string,
    public endpoint?: string
  ) {
    super(message);
    this.name = 'APIError';
  }
}

// React Query error handling
const useSalesDataWithErrorHandling = (params: SalesParams) => {
  return useQuery({
    queryKey: ['sales', params],
    queryFn: () => salesService.getSalesData(params),
    retry: (failureCount, error) => {
      if (
        error instanceof APIError &&
        error.status >= 400 &&
        error.status < 500
      ) {
        return false; // Don't retry client errors
      }
      return failureCount < 3;
    },
    onError: error => {
      console.error('Sales data fetch failed:', error);
      // Show user-friendly error message
      toast.error('Failed to load sales data. Please try again.');
    },
  });
};
```

### 4. Caching Strategy

```typescript
// React Query cache configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: 3,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
  },
});

// Cache invalidation patterns
const useInvalidateQueries = () => {
  const queryClient = useQueryClient();

  return {
    invalidateSales: () => queryClient.invalidateQueries(['sales']),
    invalidateMetrics: () => queryClient.invalidateQueries(['metrics']),
    invalidateAll: () => queryClient.invalidateQueries(),
  };
};
```

## Component Integration Examples

### 1. Chart Component with Real Data

```typescript
// Before: Static data
const staticData = [
  { day: "Mon", current: 3500, lastWeek: 3800 },
  // ... more static data
];

// After: API integration
const SalesChart = () => {
  const { data: salesData, isLoading, error } = useSalesData({
    dateRange: { start: '2024-01-01', end: '2024-01-07' }
  });

  if (isLoading) return <ChartSkeleton />;
  if (error) return <ChartError error={error} />;

  const chartData = transformToChartFormat(salesData);

  return (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart data={chartData}>
        {/* Chart implementation */}
      </LineChart>
    </ResponsiveContainer>
  );
};
```

### 2. Metric Cards with Real-Time Updates

```typescript
const MetricCard = ({ metricType }: { metricType: string }) => {
  const { data: metricData, isLoading } = useMetric(metricType, {
    refetchInterval: 30000, // Update every 30 seconds
  });

  if (isLoading) return <MetricSkeleton />;

  return (
    <Card>
      <CardContent>
        <div className="text-2xl font-bold">{metricData.value}</div>
        <div className="text-sm text-muted-foreground">{metricData.subtitle}</div>
        <TrendIndicator trend={metricData.trend} value={metricData.change} />
      </CardContent>
    </Card>
  );
};
```

### 3. AI Assistant with Real Data Context

```typescript
const generateAIResponse = async (
  userMessage: string,
  context: DataContext
) => {
  // Fetch relevant real-time data based on user query
  const relevantData = await fetchContextualData(userMessage, context);

  // Generate response with actual data
  return {
    content: `Based on your current data: ${formatInsights(relevantData)}`,
    metrics: transformToInlineMetrics(relevantData),
  };
};
```

## Environment Configuration

### Development vs Production

```typescript
// Environment-based configuration
const config = {
  development: {
    apiBaseURL: 'http://localhost:3001/api',
    enableMockData: true,
    logLevel: 'debug',
  },
  production: {
    apiBaseURL: process.env.VITE_API_BASE_URL,
    enableMockData: false,
    logLevel: 'error',
  },
};

// Feature flags for gradual rollout
const featureFlags = {
  realTimeUpdates: process.env.VITE_ENABLE_REAL_TIME === 'true',
  advancedAnalytics: process.env.VITE_ENABLE_ANALYTICS === 'true',
  betaFeatures: process.env.VITE_ENABLE_BETA === 'true',
};
```

## Testing Strategy

### Mock Service Implementation

```typescript
// Mock service for testing
class MockSalesService implements SalesService {
  async getSalesData(params: SalesParams): Promise<SalesData[]> {
    // Return predictable test data
    return mockSalesData;
  }
}

// Test integration
describe('SalesChart Integration', () => {
  it('should display sales data correctly', async () => {
    const mockService = new MockSalesService();
    render(<SalesChart service={mockService} />);

    await waitFor(() => {
      expect(screen.getByTestId('sales-chart')).toBeInTheDocument();
    });
  });
});
```

### API Testing

```typescript
// API endpoint testing
describe('Sales API Integration', () => {
  it('should handle API errors gracefully', async () => {
    // Mock API failure
    jest.spyOn(salesService, 'getSalesData').mockRejectedValue(
      new APIError(500, 'Internal Server Error')
    );

    render(<SalesChart />);

    await waitFor(() => {
      expect(screen.getByText(/failed to load/i)).toBeInTheDocument();
    });
  });
});
```

## Security Considerations

### API Key Management

- Store API keys in environment variables
- Never commit API keys to version control
- Use different keys for development/production
- Implement key rotation procedures

### Data Validation

```typescript
// Runtime type checking for API responses
import { z } from 'zod';

const SalesDataSchema = z.object({
  date: z.string(),
  amount: z.number(),
  location: z.string(),
});

const validateSalesData = (data: unknown): SalesData => {
  return SalesDataSchema.parse(data);
};
```

### Rate Limiting

```typescript
// Implement rate limiting for API calls
class RateLimitedService {
  private lastCall = 0;
  private minInterval = 1000; // 1 second

  async makeRequest<T>(request: () => Promise<T>): Promise<T> {
    const now = Date.now();
    const timeSinceLastCall = now - this.lastCall;

    if (timeSinceLastCall < this.minInterval) {
      await new Promise(resolve =>
        setTimeout(resolve, this.minInterval - timeSinceLastCall)
      );
    }

    this.lastCall = Date.now();
    return request();
  }
}
```

## Migration Strategy

### Phase 1: Mock Implementation

1. Create service interfaces
2. Implement mock services
3. Replace static data with service calls
4. Test with mock data

### Phase 2: Real API Integration

1. Implement actual API services
2. Add environment configuration
3. Configure error handling
4. Test with real data

### Phase 3: Optimization

1. Implement caching strategies
2. Add real-time updates
3. Optimize performance
4. Monitor and improve
