# Development Setup Guide

## Prerequisites

### Required Software

- **Node.js**: Version 18.0.0 or higher
- **Package Manager**: npm (comes with Node.js), yarn, or bun
- **Git**: For version control
- **Code Editor**: VS Code recommended with TypeScript extension

### Recommended VS Code Extensions

- TypeScript and JavaScript Language Features
- Tailwind CSS IntelliSense
- ES7+ React/Redux/React-Native snippets
- Prettier - Code formatter
- ESLint

## Installation Steps

### 1. <PERSON>lone and Setup

```bash
# Navigate to project directory
cd restaurant-analytics-platform

# Install all dependencies
npm install

# Verify installation
npm run dev
```

### 2. Environment Configuration

The application currently runs with static data and doesn't require environment variables. For future integrations:

```bash
# Create .env file (when needed)
touch .env

# Example future environment variables:
# VITE_API_BASE_URL=https://api.yourrestaurant.com
# VITE_SUPABASE_URL=your_supabase_url
# VITE_SUPABASE_ANON_KEY=your_supabase_key
```

### 3. Development Scripts

```bash
# Start development server (http://localhost:5173)
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Type checking
npm run type-check

# Linting (if configured)
npm run lint
```

## Project Dependencies

### Core Dependencies

```json
{
  "@tanstack/react-query": "^5.56.2", // Data fetching and caching
  "react": "^18.3.1", // React framework
  "react-dom": "^18.3.1", // React DOM rendering
  "react-router-dom": "^6.26.2", // Client-side routing
  "recharts": "^2.12.7", // Chart library
  "lucide-react": "^0.462.0", // Icon library
  "tailwind-merge": "^2.5.2", // Tailwind utility merging
  "class-variance-authority": "^0.7.1", // Component variants
  "clsx": "^2.1.1" // Conditional classnames
}
```

### UI Dependencies (Shadcn/UI)

```json
{
  "@radix-ui/react-*": "Various versions", // UI primitives
  "tailwindcss-animate": "^1.0.7", // Animation utilities
  "sonner": "^1.5.0", // Toast notifications
  "cmdk": "^1.0.0" // Command palette
}
```

## Development Workflow

### 1. Adding New Features

```bash
# Create new component
touch src/components/NewComponent.tsx

# Add to exports (if needed)
# Update routing in App.tsx
# Add to navigation in Sidebar.tsx
```

### 2. Component Development Pattern

```typescript
// Template for new components
import React from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface ComponentProps {
  // Define props here
}

export const NewComponent: React.FC<ComponentProps> = ({
  // destructure props
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Component Title</CardTitle>
      </CardHeader>
      <CardContent>
        {/* Component content */}
      </CardContent>
    </Card>
  );
};
```

### 3. Styling Guidelines

- Use Tailwind CSS classes exclusively
- Follow responsive design patterns: `mobile-first` approach
- Use semantic color tokens: `bg-background`, `text-foreground`, etc.
- Maintain consistent spacing: `p-4`, `gap-4`, `space-y-4`

### 4. TypeScript Best Practices

- Define interfaces for all props
- Use proper typing for data structures
- Leverage React.FC for functional components
- Export types when shared across components

## Build Configuration

### Vite Configuration

The project uses Vite with the following key configurations:

- TypeScript support enabled
- Path aliases configured (`@/` points to `src/`)
- Hot module replacement for development
- Optimized production builds

### Tailwind Configuration

Custom design system configured in:

- `tailwind.config.ts` - Theme customization
- `src/index.css` - CSS variables and base styles

## Testing (Future Implementation)

Currently, the application doesn't include testing setup. Recommended testing stack:

- **Jest**: Testing framework
- **React Testing Library**: Component testing
- **Cypress**: End-to-end testing

## Performance Considerations

- Components use React.memo where appropriate
- Charts are lazy-loaded through Recharts
- Images and assets are optimized for web
- Bundle splitting through Vite's default configuration

## Debugging Tips

- Use React Developer Tools browser extension
- Check browser console for errors and warnings
- Use TypeScript strict mode for better error catching
- Leverage VS Code's integrated debugging features
