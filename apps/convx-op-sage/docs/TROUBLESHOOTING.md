# Troubleshooting Guide

## Common Issues & Solutions

### 1. Application Won't Start

#### Symptoms

- Development server fails to start
- Port already in use errors
- Module not found errors

#### Solutions

```bash
# Check Node.js version (requires 18+)
node --version

# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Try different port
npm run dev -- --port 3000

# Check for conflicting processes
lsof -ti:5173 | xargs kill -9
```

#### Verification

```bash
# Verify installation
npm list react react-dom typescript

# Check for global package conflicts
npm list -g --depth=0
```

### 2. Chart Rendering Issues

#### Symptoms

- Charts appear blank or broken
- Responsive container errors
- Data not displaying correctly

#### Causes & Solutions

```typescript
// Issue: Charts not responsive
// Solution: Ensure proper container wrapper
const FixedChart = () => (
  <div style={{ width: '100%', height: '400px' }}>
    <ResponsiveContainer width="100%" height="100%">
      <LineChart data={data}>
        {/* Chart content */}
      </LineChart>
    </ResponsiveContainer>
  </div>
);

// Issue: Data format mismatch
// Solution: Validate data structure
const validateChartData = (data: any[]): boolean => {
  return data.every(item =>
    typeof item === 'object' &&
    item.hasOwnProperty('day') &&
    typeof item.current === 'number'
  );
};

// Issue: Window resize problems
// Solution: Force re-render on resize
useEffect(() => {
  const handleResize = () => {
    // Force chart re-render
    setKey(prevKey => prevKey + 1);
  };

  window.addEventListener('resize', handleResize);
  return () => window.removeEventListener('resize', handleResize);
}, []);
```

### 3. AI Assistant Issues

#### Symptoms

- AI responses not generating
- Chat interface hanging
- Message formatting broken

#### Debugging Steps

```typescript
// Add debugging logs to AI response generation
const generateAIResponse = (userMessage: string, currentStage: string) => {
  console.log('Generating response for:', userMessage);
  console.log('Current stage:', currentStage);

  try {
    const detectedTopic = detectTopicFromMessage(userMessage);
    console.log('Detected topic:', detectedTopic);

    const response = generateContextualResponse(
      detectedTopic,
      userMessage,
      currentStage
    );
    console.log('Generated response:', response);

    return response;
  } catch (error) {
    console.error('AI response generation failed:', error);
    return {
      content: "I'm having trouble processing your request. Please try again.",
      newStage: currentStage,
    };
  }
};

// Check conversation state
const debugConversationState = () => {
  console.log('Messages:', messages);
  console.log('Stage:', conversationStage);
  console.log('Discussed topics:', discussedTopics);
};
```

#### Common Fixes

```typescript
// Fix: Response delay timeout
const handleSendMessage = () => {
  if (message.trim()) {
    setMessages(prev => [
      ...prev,
      {
        type: 'user',
        content: message,
        timestamp: new Date().toLocaleTimeString(),
      },
    ]);

    // Increase timeout for complex responses
    setTimeout(() => {
      try {
        const response = generateAIResponse(message, conversationStage);
        setMessages(prev => [
          ...prev,
          {
            type: 'ai',
            content: response.content,
            timestamp: new Date().toLocaleTimeString(),
          },
        ]);
        setConversationStage(response.newStage);
      } catch (error) {
        console.error('Response generation failed:', error);
        // Add error message to chat
        setMessages(prev => [
          ...prev,
          {
            type: 'ai',
            content: 'Sorry, I encountered an error. Please try again.',
            timestamp: new Date().toLocaleTimeString(),
          },
        ]);
      }
    }, 2000); // Increased timeout

    setMessage('');
  }
};
```

### 4. TypeScript Compilation Errors

#### Common Errors & Solutions

```typescript
// Error: Property does not exist on type
// Solution: Define proper interfaces
interface ComponentProps {
  data: SalesData[];
  onUpdate?: (data: SalesData[]) => void;
}

// Error: Cannot find module
// Solution: Check import paths and add type declarations
declare module '*.svg' {
  const content: string;
  export default content;
}

// Error: Type 'undefined' is not assignable
// Solution: Add proper null checking
const SafeComponent = ({ data }: { data?: SalesData[] }) => {
  if (!data || data.length === 0) {
    return <div>No data available</div>;
  }

  return <Chart data={data} />;
};
```

#### TypeScript Configuration Issues

```bash
# Clear TypeScript cache
rm -rf node_modules/.cache
rm -rf dist

# Regenerate type declarations
npm run type-check

# Check TypeScript configuration
npx tsc --showConfig
```

### 5. Mobile Responsiveness Issues

#### Common Problems

```css
/* Issue: Components not responsive */
/* Solution: Use responsive utilities */
.responsive-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3;
}

/* Issue: Text too small on mobile */
/* Solution: Use responsive text sizes */
.responsive-text {
  @apply text-sm md:text-base lg:text-lg;
}

/* Issue: Touch targets too small */
/* Solution: Ensure minimum touch target size */
.touch-friendly {
  @apply min-h-[44px] min-w-[44px] p-2;
}
```

#### Testing Mobile Issues

```javascript
// Test responsive behavior
const testMobileView = () => {
  // Simulate mobile viewport
  window.resizeTo(375, 667);

  // Check for horizontal scrollbars
  const hasHorizontalScroll = document.body.scrollWidth > window.innerWidth;
  console.log('Horizontal scroll detected:', hasHorizontalScroll);

  // Test touch interactions
  const touchTargets = document.querySelectorAll('button, a, [role="button"]');
  touchTargets.forEach(target => {
    const rect = target.getBoundingClientRect();
    if (rect.width < 44 || rect.height < 44) {
      console.warn('Touch target too small:', target);
    }
  });
};
```

### 6. Performance Issues

#### Symptoms

- Slow page loads
- Laggy interactions
- High memory usage

#### Diagnostics

```typescript
// Performance monitoring
const performanceMonitor = {
  measureRenderTime: (componentName: string) => {
    const start = performance.now();

    return () => {
      const end = performance.now();
      console.log(`${componentName} render time: ${end - start}ms`);
    };
  },

  checkMemoryUsage: () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      console.log('Memory usage:', {
        used: Math.round(memory.usedJSHeapSize / 1048576) + ' MB',
        total: Math.round(memory.totalJSHeapSize / 1048576) + ' MB',
        limit: Math.round(memory.jsHeapSizeLimit / 1048576) + ' MB'
      });
    }
  },

  measureBundleSize: async () => {
    const response = await fetch('/stats.json');
    const stats = await response.json();
    console.log('Bundle analysis:', stats);
  }
};

// Use in components
const OptimizedComponent = React.memo(({ data }) => {
  const endMeasure = performanceMonitor.measureRenderTime('OptimizedComponent');

  useEffect(() => {
    endMeasure();
  });

  return <div>{/* Component content */}</div>;
});
```

#### Performance Fixes

```typescript
// Fix: Unnecessary re-renders
const MemoizedChart = React.memo(({ data, options }) => {
  return <Chart data={data} options={options} />;
}, (prevProps, nextProps) => {
  // Custom comparison for complex props
  return JSON.stringify(prevProps.data) === JSON.stringify(nextProps.data);
});

// Fix: Large bundle size
// Use dynamic imports for heavy components
const HeavyChart = lazy(() => import('./HeavyChart'));

const LazyLoadedChart = ({ data }) => (
  <Suspense fallback={<ChartSkeleton />}>
    <HeavyChart data={data} />
  </Suspense>
);

// Fix: Memory leaks
const ComponentWithCleanup = () => {
  useEffect(() => {
    const interval = setInterval(() => {
      // Regular updates
    }, 1000);

    // Cleanup function
    return () => clearInterval(interval);
  }, []);

  return <div>Component content</div>;
};
```

### 7. Build & Deployment Issues

#### Build Failures

```bash
# Clear build cache
rm -rf dist .vite

# Check for syntax errors
npm run type-check

# Build with verbose output
npm run build -- --mode development

# Analyze bundle
npm run build
npx vite-bundle-analyzer dist
```

#### Deployment Problems

```bash
# Verify build output
ls -la dist/

# Test production build locally
npm run preview

# Check for missing files
find dist -name "*.js" -o -name "*.css" -o -name "*.html"

# Validate HTML
npx html-validate dist/index.html
```

### 8. Browser Compatibility Issues

#### Testing Across Browsers

```javascript
// Feature detection
const checkBrowserCompatibility = () => {
  const features = {
    es6: () => {
      try {
        eval('(x => x)');
        return true;
      } catch {
        return false;
      }
    },
    fetch: () => 'fetch' in window,
    localStorage: () => {
      try {
        localStorage.setItem('test', 'test');
        localStorage.removeItem('test');
        return true;
      } catch {
        return false;
      }
    },
    webGL: () => {
      const canvas = document.createElement('canvas');
      return !!(
        canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
      );
    },
  };

  Object.entries(features).forEach(([feature, test]) => {
    console.log(`${feature}: ${test() ? 'supported' : 'not supported'}`);
  });
};
```

#### Polyfills & Fallbacks

```typescript
// Add polyfills for older browsers
if (!window.ResizeObserver) {
  import('resize-observer-polyfill').then(module => {
    window.ResizeObserver = module.default;
  });
}

// Fallback for unsupported features
const SafeChart = ({ data }) => {
  const [isSupported, setIsSupported] = useState(true);

  useEffect(() => {
    // Check if browser supports required features
    if (!window.ResizeObserver || !window.IntersectionObserver) {
      setIsSupported(false);
    }
  }, []);

  if (!isSupported) {
    return <SimpleTable data={data} />;
  }

  return <InteractiveChart data={data} />;
};
```

## Diagnostic Tools

### Browser Developer Tools

1. **Console**: Check for JavaScript errors and warnings
2. **Network**: Verify resource loading and API calls
3. **Performance**: Profile runtime performance
4. **Application**: Inspect localStorage and session data
5. **Elements**: Debug CSS and layout issues

### React Developer Tools

```bash
# Install React Developer Tools browser extension
# Use to inspect component hierarchy and props
```

### Performance Profiling

```typescript
// Add performance markers
const profileComponent = (name: string) => {
  performance.mark(`${name}-start`);

  return () => {
    performance.mark(`${name}-end`);
    performance.measure(name, `${name}-start`, `${name}-end`);

    const measure = performance.getEntriesByName(name)[0];
    console.log(`${name} took ${measure.duration}ms`);
  };
};

// Usage in components
const MyComponent = () => {
  const endProfile = profileComponent('MyComponent');

  useEffect(() => {
    endProfile();
  });

  return <div>Component content</div>;
};
```

## Emergency Procedures

### Application Down

1. Check hosting service status
2. Verify DNS resolution
3. Check SSL certificate validity
4. Review recent deployments
5. Rollback to last known working version

### Critical Performance Issues

1. Identify bottleneck using browser tools
2. Check for memory leaks
3. Analyze bundle size
4. Implement emergency optimizations
5. Consider temporary feature disabling

### Data Display Issues

1. Verify data source availability
2. Check data transformation logic
3. Validate component prop types
4. Test with minimal data set
5. Implement graceful degradation

## Getting Help

### Documentation Resources

- [React Documentation](https://react.dev/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Recharts Documentation](https://recharts.org/en-US/)
- [Vite Documentation](https://vitejs.dev/guide/)

### Community Support

- Stack Overflow (tag with specific technology)
- React Discord Community
- GitHub Issues for specific packages

### Internal Support

- Review this troubleshooting guide
- Check maintenance documentation
- Contact development team handoff contact
- Escalate to technical team if needed

## Preventive Measures

### Regular Maintenance

- Weekly dependency updates
- Monthly performance audits
- Quarterly browser compatibility testing
- Semi-annual security reviews

### Monitoring Setup

```typescript
// Error boundary for catching React errors
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
    // Send error to monitoring service
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback error={this.state.error} />;
    }

    return this.props.children;
  }
}
```

### Testing Checklist

- [ ] All major features working
- [ ] Mobile responsiveness verified
- [ ] Cross-browser compatibility tested
- [ ] Performance benchmarks met
- [ ] Error handling functional
- [ ] Accessibility standards maintained
