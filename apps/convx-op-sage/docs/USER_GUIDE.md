# User Guide - Restaurant Operations Analytics Platform

## Getting Started

### Accessing the Platform

1. **Launch Application**: Open your web browser and navigate to the platform URL
2. **Main Interface**: The platform opens to the Performance Dashboard
3. **Navigation**: Use the sidebar menu to access different sections

### System Requirements

- **Browser**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Screen Resolution**: Minimum 1024x768 (optimized for 1920x1080)
- **Internet Connection**: Required for full functionality
- **JavaScript**: Must be enabled

## Main Dashboard Overview

### Key Performance Indicators (KPIs)

The dashboard displays six primary metrics at the top:

#### 1. Total Sales

- **Current Value**: Shows current period sales (e.g., $42,500)
- **Comparison**: Week-over-week comparison with percentage change
- **Color Coding**:
  - Green: Positive performance
  - Red: Declining performance
  - Blue: Neutral/baseline

#### 2. Average Check Size

- **Purpose**: Tracks average transaction value
- **Calculation**: Total sales ÷ Number of transactions
- **Trend Indicator**: Shows if customers are spending more or less

#### 3. Customer Count

- **Metric**: Total number of customers served
- **Usage**: Track foot traffic and popularity trends
- **Analysis**: Compare with sales to understand spending patterns

#### 4. Labor Cost %

- **Calculation**: Labor costs as percentage of total sales
- **Target Range**: Typically 25-35% for restaurants
- **Interpretation**: Higher percentages may indicate overstaffing

#### 5. Food Cost %

- **Calculation**: Food costs as percentage of total sales
- **Target Range**: Typically 28-35% for restaurants
- **Usage**: Monitor ingredient costs and waste

#### 6. Customer Sentiment

- **Scale**: 1-5 star rating system
- **Source**: Aggregated customer reviews and feedback
- **Trend**: Shows if customer satisfaction is improving

### Interactive Charts

#### Sales Trend Chart

**Purpose**: Visualize daily sales performance over time

**Features**:

- **Multi-line Comparison**: Current week vs. last week vs. year ago
- **Interactive Tooltips**: Hover to see exact values
- **Responsive Design**: Adapts to screen size
- **Data Points**: Each day shows specific sales figures

**How to Read**:

1. **Blue Line (Solid)**: Current week performance
2. **Green Line (Dashed)**: Previous week comparison
3. **Purple Line (Dotted)**: Same period last year
4. **Trend Analysis**: Compare slopes to identify patterns

#### Cost Analysis Chart

**Purpose**: Track operational costs over time

**Components**:

- Labor cost trends
- Food cost trends
- Total operational expenses
- Efficiency metrics

### Performance Summary Cards

Located below the main charts, these provide weekly summaries:

#### Week-over-Week Comparison

- **Current Week Total**: Sum of all daily sales
- **Percentage Change**: Positive or negative trend indicator
- **Context**: Additional metrics like daily averages

#### Year-over-Year Analysis

- **Annual Comparison**: Current performance vs. same period last year
- **Growth Indicators**: Long-term trend analysis
- **Seasonal Adjustments**: Account for seasonal variations

## AI Assistant (OpSage)

### Accessing OpSage

1. Click "OpSage AI Assistant" in the sidebar
2. The interface loads with an initial analysis already displayed
3. Use the chat interface to ask specific questions

### Understanding AI Responses

#### Initial Analysis

OpSage automatically provides a comprehensive analysis including:

- **Customer Feedback Breakdown**: Percentage breakdown of common issues
- **Sample Comments**: Real customer feedback examples
- **Social Media Sentiment**: Trending hashtags and mentions
- **Primary Issues**: Main problems identified in the data

#### Interactive Features

##### Suggested Questions

Located below the chat interface, these buttons provide quick access to common queries:

- **Performance Analysis**: "What factors are affecting performance?"
- **Location Insights**: "Which locations need attention?"
- **Financial Impact**: "What's the revenue impact?"
- **Competitive Analysis**: "How do we compare to competitors?"

##### Inline Metrics

AI responses include visual metric cards showing:

- **Color-coded Values**: Red (concerning), Orange (caution), Green (good), Blue (neutral)
- **Key Performance Indicators**: Relevant metrics for the discussion
- **Alert Indicators**: Important warnings or notifications

### Asking Effective Questions

#### Topic-Based Queries

OpSage recognizes different types of questions:

**Performance Questions**:

- "Show me the performance breakdown"
- "What are our key metrics?"
- "How are we performing against targets?"

**Location Questions**:

- "Which locations are underperforming?"
- "Compare performance by location"
- "Show me regional insights"

**Financial Questions**:

- "What's the financial impact?"
- "Show me cost analysis"
- "How much revenue are we losing?"

**Competitive Questions**:

- "How do we compare to competitors?"
- "What's our market position?"
- "Show me competitive analysis"

#### Getting Detailed Reports

1. Ask: "Generate a report" or "Create an executive summary"
2. OpSage will show report configuration options
3. Select your preferred format (PDF, detailed analysis, presentation)
4. Confirm generation to receive the report

### Interpreting AI Analysis

#### Metric Cards

When OpSage displays inline metrics:

- **Red Metrics**: Require immediate attention
- **Orange Metrics**: Need monitoring and improvement
- **Green Metrics**: Performing well
- **Blue Metrics**: Neutral or informational

#### Trend Indicators

- **↑ Positive Trends**: Performance improving
- **↓ Negative Trends**: Performance declining
- **→ Stable Trends**: Performance consistent

#### Alert Messages

Red-bordered alerts indicate:

- Critical performance issues
- Consecutive days of decline
- Targets significantly missed
- Urgent action required

## Reports Section

### Available Reports

Navigate to the Reports section for detailed analysis documents:

#### Executive Summary Reports

- High-level overview of key metrics
- Trend analysis and insights
- Action recommendations
- Suitable for executive briefings

#### Detailed Performance Reports

- Comprehensive metric breakdowns
- Location-specific analysis
- Historical comparisons
- Operational insights

#### Custom Reports

- User-defined parameters
- Specific date ranges
- Filtered by location or metric
- Exportable formats

## Best Practices

### Daily Usage

1. **Morning Review**: Check overnight performance and alerts
2. **Metric Monitoring**: Review key KPIs for any significant changes
3. **Trend Analysis**: Compare current performance to historical data
4. **Action Items**: Address any red-flagged metrics promptly

### Weekly Analysis

1. **Comprehensive Review**: Use AI assistant for deep-dive analysis
2. **Location Comparison**: Identify best and worst performing locations
3. **Trend Identification**: Look for patterns in weekly data
4. **Strategic Planning**: Use insights for upcoming week planning

### Monthly Reporting

1. **Generate Reports**: Create monthly executive summaries
2. **Trend Analysis**: Identify longer-term patterns
3. **Benchmarking**: Compare against industry standards
4. **Strategic Adjustments**: Make operational changes based on data

### Effective AI Interaction

1. **Be Specific**: Ask targeted questions for better responses
2. **Follow Up**: Use suggested questions to dive deeper
3. **Context Awareness**: Build on previous conversation topics
4. **Action-Oriented**: Ask for specific recommendations and next steps

## Troubleshooting Common Issues

### Chart Not Loading

1. Refresh the browser page
2. Check internet connection
3. Try a different browser
4. Clear browser cache

### AI Assistant Not Responding

1. Check if message was sent successfully
2. Wait for processing (responses take 1-2 seconds)
3. Try rephrasing your question
4. Refresh the page if needed

### Mobile Display Issues

1. Rotate device to landscape mode for better chart viewing
2. Use zoom controls for detailed chart inspection
3. Scroll horizontally for full table views
4. Use tablet or desktop for optimal experience

### Data Seems Incorrect

1. Verify you're looking at the correct time period
2. Check if data is sample/demo data vs. live data
3. Compare with other metrics for consistency
4. Contact support if discrepancies persist

## Advanced Features

### Keyboard Shortcuts

- **Enter**: Send message in AI chat
- **Tab**: Navigate through interface elements
- **Escape**: Close modals or cancel actions
- **Arrow Keys**: Navigate charts and data points

### Accessibility Features

- **Screen Reader Support**: Full ARIA label implementation
- **Keyboard Navigation**: Complete interface accessible via keyboard
- **High Contrast Mode**: Browser high contrast mode supported
- **Zoom Support**: Interface scales with browser zoom

### Export Options

- **Chart Images**: Right-click charts to save as images
- **Data Export**: Available through Reports section
- **Print Friendly**: Use browser print function for dashboard snapshots
- **Report Downloads**: PDF generation through AI assistant

## Getting Help

### In-App Support

- Use AI assistant for immediate help with data interpretation
- Check suggested questions for common queries
- Review contextual tooltips throughout the interface

### Documentation

- Refer to this user guide for detailed instructions
- Check troubleshooting section for common issues
- Review feature documentation for advanced usage

### Technical Support

- Contact your system administrator for technical issues
- Report bugs or feature requests through appropriate channels
- Provide specific error messages and steps to reproduce issues

Remember: This platform is designed to provide actionable insights for restaurant operations. Focus on using the data to make informed decisions that improve customer experience and operational efficiency.
