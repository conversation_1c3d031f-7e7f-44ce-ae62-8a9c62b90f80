import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { TrendingDown, Calendar } from 'lucide-react';

export const AnalysisResults: React.FC = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingDown className="h-5 w-5 text-red-600" />
          Spicy Tuna Bowl - Critical Performance Analysis
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-red-50 rounded-lg border border-red-200">
            <div className="text-2xl font-bold text-red-600">-15%</div>
            <div className="text-xs text-red-600">Forecast</div>
          </div>
          <div className="text-center p-3 bg-red-50 rounded-lg border border-red-200">
            <div className="text-2xl font-bold text-red-600">$2,450</div>
            <div className="text-xs text-red-600">Revenue Gap</div>
          </div>
          <div className="text-center p-3 bg-orange-50 rounded-lg border border-orange-200">
            <div className="text-2xl font-bold text-orange-600">-8%</div>
            <div className="text-xs text-orange-600">Repeat Purchase</div>
          </div>
          <div className="text-center p-3 bg-orange-50 rounded-lg border border-orange-200">
            <div className="text-2xl font-bold text-orange-600">3.2/5</div>
            <div className="text-xs text-orange-600">Sentiment</div>
          </div>
        </div>

        <div className="bg-red-50 p-3 rounded-lg border border-red-200">
          <div className="flex items-center gap-2 text-red-600">
            <Calendar className="h-4 w-4" />
            <span className="text-sm font-medium">
              3 consecutive days of declining performance
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
