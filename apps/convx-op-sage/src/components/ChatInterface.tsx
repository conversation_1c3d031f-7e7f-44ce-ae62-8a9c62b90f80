import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { MessageBubble } from './chat/MessageBubble';
import { ChatInput } from './chat/ChatInput';

interface Message {
  type: 'ai' | 'user';
  content: string;
  timestamp: string;
}

interface ChatInterfaceProps {
  messages: Message[];
  message: string;
  setMessage: (message: string) => void;
  handleSendMessage: () => void;
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({
  messages,
  message,
  setMessage,
  handleSendMessage,
}) => {
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">Analysis Chat</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="h-[calc(100vh-300px)] overflow-y-auto space-y-4 p-4 bg-gray-50 rounded-lg">
          {messages.map((msg, index) => (
            <MessageBubble key={index} message={msg} />
          ))}
        </div>

        <ChatInput
          message={message}
          setMessage={setMessage}
          handleSendMessage={handleSendMessage}
        />
      </CardContent>
    </Card>
  );
};
