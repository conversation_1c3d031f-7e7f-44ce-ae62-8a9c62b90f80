import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';

export const CustomerSentimentCard: React.FC = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Customer Sentiment</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm">Positive</span>
            <div className="flex-1 mx-2 bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-500 h-2 rounded-full"
                style={{ width: '32%' }}
              ></div>
            </div>
            <span className="text-sm">32%</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm">Neutral</span>
            <div className="flex-1 mx-2 bg-gray-200 rounded-full h-2">
              <div
                className="bg-yellow-500 h-2 rounded-full"
                style={{ width: '43%' }}
              ></div>
            </div>
            <span className="text-sm">43%</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm">Negative</span>
            <div className="flex-1 mx-2 bg-gray-200 rounded-full h-2">
              <div
                className="bg-red-500 h-2 rounded-full"
                style={{ width: '25%' }}
              ></div>
            </div>
            <span className="text-sm">25%</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
