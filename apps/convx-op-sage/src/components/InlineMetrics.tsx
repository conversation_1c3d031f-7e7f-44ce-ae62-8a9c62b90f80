import React from 'react';
import { TrendingDown, Calendar } from 'lucide-react';

interface MetricData {
  value: string;
  label: string;
  color: 'red' | 'orange' | 'green' | 'blue';
}

interface InlineMetricsProps {
  title: string;
  icon?: React.ReactNode;
  metrics: MetricData[];
  alert?: {
    text: string;
    icon?: React.ReactNode;
  };
}

export const InlineMetrics: React.FC<InlineMetricsProps> = ({
  title,
  icon,
  metrics,
  alert,
}) => {
  const getColorClasses = (color: string) => {
    switch (color) {
      case 'red':
        return 'bg-red-50 border-red-200 text-red-600';
      case 'orange':
        return 'bg-orange-50 border-orange-200 text-orange-600';
      case 'green':
        return 'bg-green-50 border-green-200 text-green-600';
      case 'blue':
        return 'bg-blue-50 border-blue-200 text-blue-600';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-600';
    }
  };

  return (
    <div className="my-3 p-4 bg-white rounded-lg border shadow-sm">
      <div className="flex items-center gap-2 mb-3">
        {icon}
        <h4 className="font-medium text-sm">{title}</h4>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-3">
        {metrics.map((metric, index) => (
          <div
            key={index}
            className={`text-center p-3 rounded-lg border ${getColorClasses(metric.color)}`}
          >
            <div className="text-xl font-bold">{metric.value}</div>
            <div className="text-xs">{metric.label}</div>
          </div>
        ))}
      </div>

      {alert && (
        <div className="bg-red-50 p-3 rounded-lg border border-red-200">
          <div className="flex items-center gap-2 text-red-600">
            {alert.icon}
            <span className="text-sm font-medium">{alert.text}</span>
          </div>
        </div>
      )}
    </div>
  );
};
