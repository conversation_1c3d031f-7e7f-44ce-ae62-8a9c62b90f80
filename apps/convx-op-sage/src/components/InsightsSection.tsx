import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';

const insights = [
  {
    category: 'Sales Performance',
    description: 'Sales have decreased 5% compared to last week.',
    recommendation: 'Recommendation: XYZ',
  },
  {
    category: 'Customer Satisfaction',
    description: 'Customer Satisfaction',
  },
  {
    category: 'Labor Costs',
    description: 'Labor Costs',
  },
  {
    category: 'Sales Optimization',
    description: 'Sales Optimization',
  },
];

export function InsightsSection() {
  return (
    <Card className="col-span-2">
      <CardHeader>
        <CardTitle>Key Insights & Recommendations</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-6">
          {insights.map((insight, index) => (
            <div key={index} className="space-y-2">
              <h4 className="font-semibold text-foreground">
                {insight.category}
              </h4>
              <p className="text-sm text-muted-foreground">
                {insight.description}
              </p>
              {insight.recommendation && (
                <p className="text-sm text-blue-600">
                  {insight.recommendation}
                </p>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
