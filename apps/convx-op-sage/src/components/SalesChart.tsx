import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  Legend,
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const data = [
  { day: 'Mon', current: 3500, lastWeek: 3800, yearAgo: 3200 },
  { day: 'Tue', current: 3200, lastWeek: 3600, yearAgo: 3100 },
  { day: 'Wed', current: 4200, lastWeek: 4000, yearAgo: 3900 },
  { day: 'Thu', current: 4800, lastWeek: 5000, yearAgo: 4500 },
  { day: 'Fri', current: 5200, lastWeek: 5400, yearAgo: 4800 },
  { day: 'Sat', current: 5500, lastWeek: 5800, yearAgo: 5200 },
  { day: 'Sun', current: 5800, lastWeek: 6000, yearAgo: 5500 },
];

export function SalesChart() {
  // Calculate weekly totals for comparison
  const currentWeek = data.reduce((sum, day) => sum + day.current, 0);
  const lastWeek = data.reduce((sum, day) => sum + day.lastWeek, 0);
  const yearAgo = data.reduce((sum, day) => sum + day.yearAgo, 0);

  const weekOverWeekChange = (
    ((currentWeek - lastWeek) / lastWeek) *
    100
  ).toFixed(1);
  const yearOverYearChange = (
    ((currentWeek - yearAgo) / yearAgo) *
    100
  ).toFixed(1);

  return (
    <Card className="col-span-2">
      <CardHeader>
        <CardTitle>Daily Sales Trend</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis
                dataKey="day"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#666' }}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#666' }}
                tickFormatter={value => `$${value}`}
              />
              <Tooltip
                formatter={(value, name) => {
                  const labels = {
                    current: 'This Week',
                    lastWeek: 'Last Week',
                    yearAgo: 'Year Ago',
                  };
                  return [
                    `$${value}`,
                    labels[name as keyof typeof labels] || name,
                  ];
                }}
                labelStyle={{ color: '#666' }}
                contentStyle={{
                  backgroundColor: '#fff',
                  border: '1px solid #e2e8f0',
                  borderRadius: '6px',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                }}
              />
              <Legend
                verticalAlign="top"
                height={36}
                iconType="line"
                wrapperStyle={{ paddingBottom: '20px' }}
              />
              <Line
                type="monotone"
                dataKey="current"
                stroke="#3b82f6"
                strokeWidth={3}
                dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
                name="This Week"
              />
              <Line
                type="monotone"
                dataKey="lastWeek"
                stroke="#10b981"
                strokeWidth={2}
                strokeDasharray="5 5"
                dot={{ fill: '#10b981', strokeWidth: 2, r: 3 }}
                activeDot={{ r: 5, stroke: '#10b981', strokeWidth: 2 }}
                name="Last Week"
              />
              <Line
                type="monotone"
                dataKey="yearAgo"
                stroke="#6b7280"
                strokeWidth={2}
                strokeDasharray="2 2"
                dot={{ fill: '#6b7280', strokeWidth: 2, r: 3 }}
                activeDot={{ r: 5, stroke: '#6b7280', strokeWidth: 2 }}
                name="Year Ago"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
        <div className="mt-4 grid grid-cols-2 gap-3">
          <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="text-sm">
              <span className="font-medium text-blue-900">Week over Week</span>
              <div className="text-blue-700">
                Current:{' '}
                <span className="font-semibold">
                  ${currentWeek.toLocaleString()}
                </span>
              </div>
              <div
                className={`text-sm font-semibold ${parseFloat(weekOverWeekChange) >= 0 ? 'text-green-600' : 'text-red-600'}`}
              >
                {parseFloat(weekOverWeekChange) >= 0 ? '+' : ''}
                {weekOverWeekChange}% vs last week
              </div>
            </div>
          </div>
          <div className="p-3 bg-gray-50 rounded-lg border border-gray-200">
            <div className="text-sm">
              <span className="font-medium text-gray-900">Year over Year</span>
              <div className="text-gray-700">
                Year Ago:{' '}
                <span className="font-semibold">
                  ${yearAgo.toLocaleString()}
                </span>
              </div>
              <div
                className={`text-sm font-semibold ${parseFloat(yearOverYearChange) >= 0 ? 'text-green-600' : 'text-red-600'}`}
              >
                {parseFloat(yearOverYearChange) >= 0 ? '+' : ''}
                {yearOverYearChange}% vs year ago
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
