import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';

const salesData = [
  { day: 'Mon', actual: 4200, forecasted: 4500, lastWeek: 4100, yearAgo: 3800 },
  { day: 'Tue', actual: 3800, forecasted: 4300, lastWeek: 4000, yearAgo: 3600 },
  { day: 'Wed', actual: 4500, forecasted: 4600, lastWeek: 4400, yearAgo: 4200 },
  { day: 'Thu', actual: 4800, forecasted: 5000, lastWeek: 5200, yearAgo: 4600 },
  { day: 'Fri', actual: 5200, forecasted: 5400, lastWeek: 5600, yearAgo: 4900 },
  { day: 'Sat', actual: 5500, forecasted: 5600, lastWeek: 5800, yearAgo: 5300 },
  { day: 'Sun', actual: 5800, forecasted: 5800, lastWeek: 6100, yearAgo: 5500 },
];

export const SalesPerformanceChart: React.FC = () => {
  const actualTotal = salesData.reduce((sum, day) => sum + day.actual, 0);
  const lastWeekTotal = salesData.reduce((sum, day) => sum + day.lastWeek, 0);
  const yearAgoTotal = salesData.reduce((sum, day) => sum + day.yearAgo, 0);

  const weekOverWeekChange = (
    ((actualTotal - lastWeekTotal) / lastWeekTotal) *
    100
  ).toFixed(1);
  const yearOverYearChange = (
    ((actualTotal - yearAgoTotal) / yearAgoTotal) *
    100
  ).toFixed(1);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Sales Performance</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-48 mb-4">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={salesData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis
                dataKey="day"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 11, fill: '#666' }}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 11, fill: '#666' }}
                tickFormatter={value => `${value}`}
              />
              <Tooltip
                formatter={(value, name) => {
                  const labels = {
                    actual: 'Actual Sales',
                    forecasted: 'Forecasted Sales',
                    lastWeek: 'Last Week',
                    yearAgo: 'Year Ago',
                  };
                  return [
                    `$${value}`,
                    labels[name as keyof typeof labels] || name,
                  ];
                }}
                labelStyle={{ color: '#666' }}
                contentStyle={{
                  backgroundColor: '#fff',
                  border: '1px solid #e2e8f0',
                  borderRadius: '6px',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                }}
              />
              <Legend
                verticalAlign="top"
                height={30}
                iconType="line"
                wrapperStyle={{ fontSize: '10px', paddingBottom: '10px' }}
              />
              <Line
                type="monotone"
                dataKey="actual"
                stroke="#ef4444"
                strokeWidth={2}
                dot={{ fill: '#ef4444', strokeWidth: 2, r: 3 }}
                name="Actual"
              />
              <Line
                type="monotone"
                dataKey="forecasted"
                stroke="#6b7280"
                strokeWidth={2}
                strokeDasharray="5 5"
                dot={{ fill: '#6b7280', strokeWidth: 2, r: 3 }}
                name="Forecast"
              />
              <Line
                type="monotone"
                dataKey="lastWeek"
                stroke="#10b981"
                strokeWidth={1.5}
                strokeDasharray="3 3"
                dot={{ fill: '#10b981', strokeWidth: 1, r: 2 }}
                name="Last Week"
              />
              <Line
                type="monotone"
                dataKey="yearAgo"
                stroke="#8b5cf6"
                strokeWidth={1.5}
                strokeDasharray="1 3"
                dot={{ fill: '#8b5cf6', strokeWidth: 1, r: 2 }}
                name="Year Ago"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>vs Last Week</span>
            <span
              className={
                parseFloat(weekOverWeekChange) >= 0
                  ? 'text-green-600'
                  : 'text-red-600'
              }
            >
              {parseFloat(weekOverWeekChange) >= 0 ? '+' : ''}
              {weekOverWeekChange}%
            </span>
          </div>
          <div className="flex justify-between text-sm">
            <span>vs Year Ago</span>
            <span
              className={
                parseFloat(yearOverYearChange) >= 0
                  ? 'text-green-600'
                  : 'text-red-600'
              }
            >
              {parseFloat(yearOverYearChange) >= 0 ? '+' : ''}
              {yearOverYearChange}%
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
