import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { MapPin, Circle } from 'lucide-react';
import { storeLocations } from '@/constants/data';
import { getStatusBadge } from '@/utils/badgeHelpers';

export const StoreLocationsCard: React.FC = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Florida Store Locations</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-32 bg-gradient-to-b from-blue-50 to-green-50 rounded-lg flex items-center justify-center mb-4 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-100 via-transparent to-green-100 opacity-50"></div>
          <div className="relative z-10 flex items-center gap-2">
            <MapPin className="h-5 w-5 text-blue-600" />
            <span className="text-sm font-medium">Florida State Map</span>
          </div>
          {/* Simulated store dots */}
          <div className="absolute top-3 left-8 w-2 h-2 bg-red-500 rounded-full"></div>
          <div className="absolute top-6 right-12 w-2 h-2 bg-red-500 rounded-full"></div>
          <div className="absolute bottom-8 left-1/2 w-2 h-2 bg-blue-500 rounded-full"></div>
          <div className="absolute bottom-4 right-8 w-2 h-2 bg-green-500 rounded-full"></div>
          <div className="absolute top-1/2 left-6 w-2 h-2 bg-orange-500 rounded-full"></div>
        </div>

        {/* Performance Legend */}
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <div className="text-xs font-medium mb-2">
            Performance vs State Avg (32%)
          </div>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex items-center gap-1">
              <Circle className="h-2 w-2 text-red-500 fill-current" />
              <span>Poor (&lt;-15%)</span>
            </div>
            <div className="flex items-center gap-1">
              <Circle className="h-2 w-2 text-orange-500 fill-current" />
              <span>Below Avg</span>
            </div>
            <div className="flex items-center gap-1">
              <Circle className="h-2 w-2 text-yellow-500 fill-current" />
              <span>Average</span>
            </div>
            <div className="flex items-center gap-1">
              <Circle className="h-2 w-2 text-green-500 fill-current" />
              <span>Above Avg</span>
            </div>
          </div>
        </div>

        {/* Top and Bottom Performers */}
        <div className="space-y-2 max-h-48 overflow-y-auto">
          <div className="text-xs font-medium text-red-600 mb-1">
            Worst Performers
          </div>
          {storeLocations
            .filter(store => store.status === 'poor')
            .map((store, index) => (
              <div
                key={index}
                className="flex items-center justify-between text-xs"
              >
                <span className="truncate">{store.name}</span>
                {getStatusBadge(store.status, store.performance)}
              </div>
            ))}

          <div className="text-xs font-medium text-orange-600 mb-1 mt-3">
            Below Average
          </div>
          {storeLocations
            .filter(store => store.status === 'below_average')
            .slice(0, 3)
            .map((store, index) => (
              <div
                key={index}
                className="flex items-center justify-between text-xs"
              >
                <span className="truncate">{store.name}</span>
                {getStatusBadge(store.status, store.performance)}
              </div>
            ))}

          <div className="text-xs font-medium text-green-600 mb-1 mt-3">
            Top Performers
          </div>
          {storeLocations
            .filter(store => store.status === 'above_average')
            .map((store, index) => (
              <div
                key={index}
                className="flex items-center justify-between text-xs"
              >
                <span className="truncate">{store.name}</span>
                {getStatusBadge(store.status, store.performance)}
              </div>
            ))}
        </div>
      </CardContent>
    </Card>
  );
};
