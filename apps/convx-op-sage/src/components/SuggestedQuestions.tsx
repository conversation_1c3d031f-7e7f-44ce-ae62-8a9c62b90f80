import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { contextualQuestions } from '@/constants/data';

interface SuggestedQuestionsProps {
  setMessage: (message: string) => void;
  handleSendMessage: (question: string) => void;
  conversationStage?: string;
}

export const SuggestedQuestions: React.FC<SuggestedQuestionsProps> = ({
  setMessage,
  handleSendMessage,
  conversationStage = 'initial',
}) => {
  const questions =
    contextualQuestions[
      conversationStage as keyof typeof contextualQuestions
    ] || contextualQuestions.initial;

  const handleQuestionClick = (question: string) => {
    handleSendMessage(question);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Suggested Questions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          {questions.map((question, index) => (
            <Button
              key={index}
              variant="outline"
              className="justify-start text-left h-auto p-3 hover:bg-blue-50 hover:border-blue-300 transition-colors"
              onClick={() => handleQuestionClick(question)}
            >
              {question}
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
