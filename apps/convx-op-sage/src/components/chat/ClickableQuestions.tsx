import React from 'react';

interface ClickableQuestionsProps {
  beforeQuestions?: string;
  afterQuestions?: string;
  formatContent: (content: string) => React.ReactNode;
}

export const ClickableQuestions: React.FC<ClickableQuestionsProps> = ({
  beforeQuestions,
  afterQuestions,
  formatContent,
}) => {
  const handleQuestionClick = (question: string) => {
    const event = new CustomEvent('questionClick', {
      detail: { question },
    });
    window.dispatchEvent(event);
  };

  // Enhanced function to handle multiple inline images
  const formatContentWithImages = (content: string) => {
    if (!content) return null;

    // Split on all INLINE_IMAGE markers, keep them in the array
    const parts = content.split(/(\*\*INLINE_IMAGE:[^*]+\*\*)/g);

    return (
      <div className="space-y-3">
        {parts.map((part, i) => {
          const imageMatch = part.match(/\*\*INLINE_IMAGE:([^*]+)\*\*/);
          if (imageMatch) {
            const imagePath = imageMatch[1].trim();
            return (
              <div key={i} className="w-full bg-white border rounded-lg p-4">
                <img
                  src={imagePath}
                  alt="Performance Analysis Chart"
                  className="w-full h-auto rounded-lg"
                />
              </div>
            );
          } else if (part.trim()) {
            // Plain text content
            return <div key={i}>{formatContent(part)}</div>;
          }
          return null;
        })}
      </div>
    );
  };

  // Check if this is for Yes/No questions
  const isYesNoQuestion = beforeQuestions?.includes(
    '**CLICKABLE_QUESTIONS_YES_NO**'
  );

  // Check if this is for post-completion actions
  const isPostCompletion = beforeQuestions?.includes(
    '**CLICKABLE_QUESTIONS_POST_COMPLETION**'
  );

  let questions: string[] = [];

  if (isYesNoQuestion) {
    questions = ['Yes', 'No'];
  } else if (isPostCompletion) {
    questions = [
      'Add a daily performance tracker to your dashboard',
      'Schedule a team meeting to review results',
      'Share this analysis with regional managers',
    ];
  } else {
    // Default original questions for the initial chat experience
    questions = [
      'What are customers saying about this item?',
      'Is this underperforming everywhere or just certain locations?',
      "What's the financial impact if we keep or remove this item?",
      'What changes could improve performance?',
    ];
  }

  return (
    <div className="space-y-3">
      {beforeQuestions && formatContentWithImages(beforeQuestions)}
      <div className="space-y-2 mt-3">
        <div className="grid grid-cols-1 gap-2">
          {questions.map((question, index) => (
            <button
              key={index}
              className="text-left p-3 text-sm bg-blue-50 hover:bg-blue-100 border border-blue-200 rounded-lg transition-colors cursor-pointer"
              onClick={() => handleQuestionClick(question)}
            >
              {question}
            </button>
          ))}
        </div>
      </div>
      {afterQuestions && formatContentWithImages(afterQuestions)}
    </div>
  );
};
