import React from 'react';

export const formatRegularContent = (content: string): React.ReactNode => {
  // Remove any CLICKABLE_QUESTIONS markers before processing
  const cleanContent = content.replace(/\*\*CLICKABLE_QUESTIONS\*\*/g, '');

  const lines = cleanContent.split('\n');
  return lines.map((line, index) => {
    // Skip processing image URLs - they should be handled by InlineImage component
    if (
      line.includes('/lovable-uploads/') &&
      (line.includes('.png') ||
        line.includes('.jpg') ||
        line.includes('.jpeg') ||
        line.includes('.gif'))
    ) {
      return null;
    }

    if (line.startsWith('**') && line.endsWith('**')) {
      return (
        <div key={index} className="font-bold text-sm mt-3 mb-1">
          {line.replace(/\*\*/g, '')}
        </div>
      );
    } else if (line.startsWith('•')) {
      return (
        <div key={index} className="text-sm ml-2 mb-1">
          {line}
        </div>
      );
    } else if (
      line.startsWith('Positive') ||
      line.startsWith('Neutral') ||
      line.startsWith('Negative')
    ) {
      const color = line.startsWith('Positive')
        ? 'text-green-600'
        : line.startsWith('Neutral')
          ? 'text-orange-600'
          : 'text-red-600';
      const bgColor = line.startsWith('Positive')
        ? 'bg-green-50'
        : line.startsWith('Neutral')
          ? 'bg-orange-50'
          : 'bg-red-50';
      return (
        <div
          key={index}
          className={`text-sm font-medium ${color} ${bgColor} px-2 py-1 rounded mb-1`}
        >
          {line}
        </div>
      );
    } else if (
      line.startsWith('🔴') ||
      line.startsWith('🟡') ||
      line.startsWith('🔵')
    ) {
      return (
        <div key={index} className="text-sm ml-2 mb-1 font-medium">
          {line}
        </div>
      );
    } else if (
      line.startsWith('☑') ||
      line.startsWith('○') ||
      line.startsWith('◉')
    ) {
      return (
        <div key={index} className="text-sm ml-2 mb-1 font-medium">
          {line}
        </div>
      );
    } else if (line.startsWith('✓')) {
      return (
        <div key={index} className="text-sm ml-2 mb-1 text-green-600">
          {line}
        </div>
      );
    } else if (
      line.includes(':') &&
      (line.includes('%') || line.includes('$') || line.includes('/5'))
    ) {
      return (
        <div key={index} className="text-sm bg-gray-50 px-2 py-1 rounded mb-1">
          {line}
        </div>
      );
    } else if (line.startsWith('"') && line.endsWith('"')) {
      return (
        <div
          key={index}
          className="text-sm italic bg-blue-50 px-2 py-1 rounded border-l-2 border-blue-200 mb-1"
        >
          {line}
        </div>
      );
    } else if (line.includes('#')) {
      return (
        <div key={index} className="text-sm text-blue-600 mb-1">
          {line}
        </div>
      );
    } else if (
      line.includes('📄') ||
      line.includes('📥') ||
      line.includes('👁')
    ) {
      return (
        <div
          key={index}
          className="text-sm font-medium bg-gray-50 px-2 py-1 rounded mb-1"
        >
          {line}
        </div>
      );
    } else if (line.trim()) {
      return (
        <div key={index} className="text-sm mb-1">
          {line}
        </div>
      );
    }
    return null;
  });
};
