import React from 'react';
import {
  TrendingDown,
  Calendar,
  MapPin,
  Users,
  DollarSign,
} from 'lucide-react';
import { InlineMetrics } from '../InlineMetrics';
import { InlineSalesChart } from './InlineSalesChart';
import { InlineStoreMap } from './InlineStoreMap';

export const renderInlineComponent = (type: string): React.ReactNode => {
  switch (type) {
    case 'sales_chart':
      return <InlineSalesChart />;
    case 'store_map':
      return <InlineStoreMap />;
    case 'lto_analysis':
      return (
        <InlineMetrics
          title="Spicy Tuna Bowl LTO - Performance Analysis"
          icon={<TrendingDown className="h-5 w-5 text-red-600" />}
          metrics={[
            { value: '$2,450', label: 'Revenue Gap/Week', color: 'red' },
            { value: '22%', label: 'Repeat Purchase', color: 'red' },
            { value: '-18%', label: 'Check Average', color: 'red' },
            { value: '$10,800', label: 'Projected Loss', color: 'red' },
          ]}
          alert={{
            text: 'LTO underperforming across all key metrics - immediate intervention required',
            icon: <Calendar className="h-4 w-4" />,
          }}
        />
      );
    case 'performance':
      return (
        <InlineMetrics
          title="Spicy Tuna Bowl - Critical Performance Analysis"
          icon={<TrendingDown className="h-5 w-5 text-red-600" />}
          metrics={[
            { value: '-15%', label: 'Forecast', color: 'red' },
            { value: '$2,450', label: 'Revenue Gap', color: 'red' },
            { value: '-8%', label: 'Repeat Purchase', color: 'orange' },
            { value: '3.2/5', label: 'Sentiment', color: 'orange' },
          ]}
          alert={{
            text: '3 consecutive days of declining performance',
            icon: <Calendar className="h-4 w-4" />,
          }}
        />
      );
    case 'financial':
      return (
        <InlineMetrics
          title="Financial Impact Analysis"
          icon={<DollarSign className="h-5 w-5 text-red-600" />}
          metrics={[
            { value: '$2,450', label: 'Weekly Gap', color: 'red' },
            { value: '$10,800', label: 'LTO Lifetime Loss', color: 'red' },
            { value: '2.3%', label: 'Margin Reduction', color: 'red' },
            { value: '45%', label: 'Improvement Needed', color: 'orange' },
          ]}
        />
      );
    case 'location':
      return (
        <InlineMetrics
          title="Store Location Performance"
          icon={<MapPin className="h-5 w-5 text-blue-600" />}
          metrics={[
            { value: '2', label: 'Underperforming', color: 'red' },
            { value: '2', label: 'Meeting Baseline', color: 'orange' },
            { value: '1', label: 'Exceeding Forecast', color: 'green' },
            { value: '50%', label: 'Performance Variance', color: 'orange' },
          ]}
        />
      );
    case 'customer_behavior':
      return (
        <InlineMetrics
          title="Customer Behavior Analysis"
          icon={<Users className="h-5 w-5 text-orange-600" />}
          metrics={[
            { value: '22%', label: 'Repeat Purchase', color: 'red' },
            { value: '-18%', label: 'Check Average', color: 'red' },
            { value: '15%', label: 'Menu Abandonment', color: 'orange' },
            { value: '3.3/5', label: 'Customer Rating', color: 'orange' },
          ]}
        />
      );
    default:
      return null;
  }
};
