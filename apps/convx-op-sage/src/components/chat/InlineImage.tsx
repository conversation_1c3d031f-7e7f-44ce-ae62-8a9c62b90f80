import React from 'react';

interface InlineImageProps {
  imagePath: string;
  beforeImage?: string;
  afterImage?: string;
  formatContent: (content: string) => React.ReactNode;
}

export const InlineImage: React.FC<InlineImageProps> = ({
  imagePath,
  beforeImage,
  afterImage,
  formatContent,
}) => {
  return (
    <div className="space-y-3">
      {beforeImage && formatContent(beforeImage)}
      <div className="w-full bg-white border rounded-lg p-4 my-3">
        <img
          src={imagePath}
          alt="Performance Analysis Chart"
          className="w-full h-auto rounded-lg"
        />
      </div>
      {afterImage && formatContent(afterImage)}
    </div>
  );
};
