import React from 'react';
import { Bot } from 'lucide-react';
import { MessageContent } from './MessageContent';

interface Message {
  type: 'ai' | 'user';
  content: string;
  timestamp: string;
}

interface MessageBubbleProps {
  message: Message;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({ message }) => {
  return (
    <div
      className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
    >
      <div
        className={`flex items-start gap-2 max-w-[85%] ${
          message.type === 'user' ? 'flex-row-reverse' : 'flex-row'
        }`}
      >
        {message.type === 'ai' && (
          <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
            <Bot className="h-4 w-4 text-white" />
          </div>
        )}

        <div
          className={`p-3 rounded-lg ${
            message.type === 'user'
              ? 'bg-blue-600 text-white'
              : 'bg-white border shadow-sm'
          }`}
        >
          <div className="space-y-1">
            {message.type === 'ai' ? (
              <MessageContent content={message.content} />
            ) : (
              <p className="text-sm">{message.content}</p>
            )}
          </div>
          <span className="text-xs opacity-70 mt-2 block">
            {message.timestamp}
          </span>
        </div>
      </div>
    </div>
  );
};
