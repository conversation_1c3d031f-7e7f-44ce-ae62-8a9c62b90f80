import React from 'react';

interface ReportStatusProps {
  content: string;
  type: 'config' | 'generating';
  formatContent: (content: string) => React.ReactNode;
}

export const ReportStatus: React.FC<ReportStatusProps> = ({
  content,
  type,
  formatContent,
}) => {
  const getStatusConfig = () => {
    if (type === 'config') {
      return {
        bgColor: 'bg-blue-50',
        borderColor: 'border-blue-200',
        textColor: 'text-blue-800',
        icon: '📊',
        title: 'Report Configuration',
      };
    }
    return {
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      textColor: 'text-green-800',
      icon: '⚡',
      title: 'Report Generation',
    };
  };

  const config = getStatusConfig();

  return (
    <div className="space-y-3">
      <div
        className={`p-3 ${config.bgColor} rounded-lg border ${config.borderColor}`}
      >
        <div className={`text-sm font-medium ${config.textColor} mb-2`}>
          {config.icon} {config.title}
        </div>
        {formatContent(content)}
      </div>
    </div>
  );
};
