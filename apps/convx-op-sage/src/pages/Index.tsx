import { SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/Sidebar';
import { Header } from '@/components/Header';
import { MetricCard } from '@/components/MetricCard';
import { SalesChart } from '@/components/SalesChart';
import { CostChart } from '@/components/CostChart';
import { InsightsSection } from '@/components/InsightsSection';
import { LocationsTable } from '@/components/LocationsTable';
import { CustomerInsights } from '@/components/CustomerInsights';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

const Index = () => {
  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <div className="flex-1">
          <Header />
          <main className="p-6">
            {/* Page Header */}
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-foreground">
                  Performance
                </h1>
                <p className="text-muted-foreground mt-1">
                  Overall Performance
                </p>
              </div>
              <div className="flex items-center gap-4">
                <Select defaultValue="7days">
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Time Range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7days">Last 7 Days</SelectItem>
                    <SelectItem value="30days">Last 30 Days</SelectItem>
                    <SelectItem value="90days">Last 90 Days</SelectItem>
                  </SelectContent>
                </Select>
                <Select defaultValue="all">
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Locations" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Locations</SelectItem>
                    <SelectItem value="location1">Location 1</SelectItem>
                    <SelectItem value="location2">Location 2</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Metrics Grid */}
            <div className="grid grid-cols-6 gap-6 mb-8">
              <MetricCard
                title="TOTAL SALES"
                value="$42,500"
                subtitle="vs 45000 same day last week"
                trend="down"
                trendValue="(-5.6%)"
                color="blue"
              />
              <MetricCard
                title="AVG CHECK SIZE"
                value="$12.45"
                subtitle="Yesterday vs. 7-day average"
                trend="down"
                trendValue=""
                color="blue"
              />
              <MetricCard
                title="CUSTOMER COUNT"
                value="1,230"
                subtitle="Total customers"
                trend="up"
                trendValue=""
                color="blue"
              />
              <MetricCard
                title="LABOR COST%"
                value="24.3%"
                subtitle="Percentage of sales"
                trend="up"
                trendValue=""
                color="green"
              />
              <MetricCard
                title="FOOD COST%"
                value="30.2%"
                subtitle="Percentage of sales"
                trend="up"
                trendValue=""
                color="green"
              />
              <MetricCard
                title="CUSTOMER SENTIMENT"
                value="4.2/5"
                subtitle="Improving"
                trend="up"
                trendValue=""
                color="green"
              />
            </div>

            {/* Charts Row */}
            <div className="grid grid-cols-3 gap-6 mb-8">
              <SalesChart />
              <CostChart />
            </div>

            {/* Insights and Locations */}
            <div className="grid grid-cols-3 gap-6 mb-8">
              <InsightsSection />
              <LocationsTable />
            </div>

            {/* Bottom Section */}
            <CustomerInsights />
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default Index;
