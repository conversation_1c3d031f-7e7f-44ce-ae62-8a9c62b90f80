import { Badge } from '@/components/ui/badge';

export const getStatusBadge = (status: string, performance: number) => {
  switch (status) {
    case 'poor':
      return <Badge variant="destructive">{performance}%</Badge>;
    case 'below_average':
      return (
        <Badge className="bg-orange-100 text-orange-800 border-orange-200">
          {performance}%
        </Badge>
      );
    case 'average':
      return <Badge variant="secondary">{performance}%</Badge>;
    case 'above_average':
      return (
        <Badge className="bg-green-100 text-green-800 border-green-200">
          +{performance}%
        </Badge>
      );
    default:
      return <Badge variant="secondary">{performance}%</Badge>;
  }
};

export const getStatusColor = (status: string) => {
  switch (status) {
    case 'poor':
      return 'text-red-600';
    case 'below_average':
      return 'text-orange-600';
    case 'average':
      return 'text-yellow-600';
    case 'above_average':
      return 'text-green-600';
    default:
      return 'text-gray-600';
  }
};
