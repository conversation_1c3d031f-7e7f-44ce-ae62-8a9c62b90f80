import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { OnboardingProvider } from '@/contexts/OnboardingContext';
import { AmplifyAuthProvider } from '@/hooks/useAmplifyAuth';
import { ProtectedRoute } from '@/components/ProtectedRoute';
import { Toaster } from '@/components/ui/toaster';
import '@/lib/amplify'; // Initialize Amplify configuration
import Index from './pages/Index';
import MasterDataMapping from './pages/MasterDataMapping';
import Integrations from './pages/Integrations';
import Analytics from './pages/Analytics';
import Alerts from './pages/Alerts';
import Settings from './pages/Settings';
import UserProfile from './pages/UserProfile';
import Auth from './pages/Auth';
import NotFound from './pages/NotFound';

// New sub-menu pages
import ConvxOps from './pages/ConvxOps';
import ConvxMarketing from './pages/ConvxMarketing';
import ConvxFinance from './pages/ConvxFinance';

// Onboarding pages
import WelcomeNewUser from './pages/onboarding/WelcomeNewUser';
import CreateCompany from './pages/onboarding/CreateCompany';
import AddLocations from './pages/onboarding/AddLocations';
import InviteTeam from './pages/onboarding/InviteTeam';
import WelcomeExistingUser from './pages/onboarding/WelcomeExistingUser';
import ProfileSetup from './pages/onboarding/ProfileSetup';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AmplifyAuthProvider>
        <OnboardingProvider>
          <BrowserRouter>
            <Routes>
              <Route path="/auth" element={<Auth />} />

              {/* Onboarding Routes */}
              <Route
                path="/onboarding/welcome-new"
                element={
                  <ProtectedRoute>
                    <WelcomeNewUser />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/onboarding/create-company"
                element={
                  <ProtectedRoute>
                    <CreateCompany />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/onboarding/add-locations"
                element={
                  <ProtectedRoute>
                    <AddLocations />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/onboarding/invite-team"
                element={
                  <ProtectedRoute>
                    <InviteTeam />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/onboarding/welcome-existing"
                element={
                  <ProtectedRoute>
                    <WelcomeExistingUser />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/onboarding/profile-setup"
                element={
                  <ProtectedRoute>
                    <ProfileSetup />
                  </ProtectedRoute>
                }
              />

              {/* Main App Routes */}
              <Route
                path="/"
                element={
                  <ProtectedRoute>
                    <Index />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/master-data"
                element={
                  <ProtectedRoute>
                    <MasterDataMapping />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/integrations"
                element={
                  <ProtectedRoute>
                    <Integrations />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/analytics"
                element={
                  <ProtectedRoute>
                    <Analytics />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/alerts"
                element={
                  <ProtectedRoute>
                    <Alerts />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/settings"
                element={
                  <ProtectedRoute>
                    <Settings />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/profile"
                element={
                  <ProtectedRoute>
                    <UserProfile />
                  </ProtectedRoute>
                }
              />

              {/* New Sub-Menu Routes */}
              <Route
                path="/convx-ops"
                element={
                  <ProtectedRoute>
                    <ConvxOps />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/convx-marketing"
                element={
                  <ProtectedRoute>
                    <ConvxMarketing />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/convx-finance"
                element={
                  <ProtectedRoute>
                    <ConvxFinance />
                  </ProtectedRoute>
                }
              />

              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
          <Toaster />
        </OnboardingProvider>
      </AmplifyAuthProvider>
    </QueryClientProvider>
  );
}

export default App;
