import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
// TODO: Replace with Amplify data layer
// import { supabase } from '@/integrations/supabase/client';
// import { Database } from '@/integrations/supabase/types';
import { useAmplifyAuth } from '@/hooks/useAmplifyAuth';

// TODO: Replace with Amplify schema types
type DataType = 'menu_items' | 'employees' | 'locations' | 'vendors';

export interface UnmatchedItem {
  id: string;
  source_value: string;
  source_system: { name: string };
  data_type: string;
  frequency: number;
  last_seen: string;
}

export interface MasterDataItem {
  id: string;
  name: string;
  description?: string;
}

export const useUnmatchedItems = (dataType: DataType) => {
  const { user } = useAmplifyAuth();

  return useQuery({
    queryKey: ['unmatched-items', dataType],
    queryFn: async () => {
      // TODO: Replace with Amplify GraphQL API
      console.log('TODO: Implement with Amplify data layer');
      return [];
    },
    enabled: !!user,
  });
};

export const useMasterData = (dataType: DataType) => {
  const { user } = useAmplifyAuth();

  return useQuery({
    queryKey: ['master-data', dataType],
    queryFn: async () => {
      // TODO: Replace with Amplify GraphQL API
      console.log(
        'TODO: Implement master data with Amplify data layer for type:',
        dataType
      );
      return [];
    },
    enabled: !!user,
  });
};

export const useCreateMatching = () => {
  const queryClient = useQueryClient();
  const { user } = useAmplifyAuth();

  return useMutation({
    mutationFn: async (matching: {
      sourceId: string;
      sourceValue: string;
      masterValue: string;
      isNewMaster: boolean;
      dataType: string;
      masterTable?: string;
      masterId?: string;
    }) => {
      if (!user) throw new Error('User not authenticated');

      // TODO: Replace with Amplify GraphQL API
      console.log('TODO: Implement data matching with Amplify data layer');
      return { success: true };
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ['unmatched-items', variables.dataType],
      });
      queryClient.invalidateQueries({
        queryKey: ['master-data', variables.dataType],
      });
    },
  });
};
