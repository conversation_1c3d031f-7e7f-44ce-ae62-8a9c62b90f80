import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
// TODO: Replace with Amplify data layer
// import { supabase } from '@/integrations/supabase/client';
import { useAmplifyAuth } from './useAmplifyAuth';

export interface RestaurantLocationForm {
  name: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  phone?: string;
  manager_name?: string;
}

export function useRestaurantLocations() {
  const { user } = useAmplifyAuth();
  const queryClient = useQueryClient();

  const createLocation = useMutation({
    mutationFn: async (locationData: RestaurantLocationForm) => {
      if (!user) throw new Error('User not authenticated');

      console.log('Creating restaurant location:', locationData);

      const { data, error } = await supabase
        .from('restaurant_locations')
        .insert({
          ...locationData,
          status: 'active',
        })
        .select()
        .single();

      if (error) {
        console.error('Location creation error:', error);
        throw error;
      }

      console.log('Created location:', data);
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['restaurant-locations'] });
    },
  });

  const createMultipleLocations = async (
    locations: RestaurantLocationForm[]
  ) => {
    const results = [];
    for (const location of locations) {
      try {
        const result = await createLocation.mutateAsync(location);
        results.push(result);
      } catch (error) {
        console.error('Failed to create location:', location.name, error);
        throw error;
      }
    }
    return results;
  };

  return {
    createLocation: createLocation.mutateAsync,
    createMultipleLocations,
    isCreating: createLocation.isPending,
    error: createLocation.error,
  };
}
