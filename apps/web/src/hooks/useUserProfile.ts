import { useState, useEffect } from 'react';
// TODO: Replace with Amplify data layer
// import { supabase } from '@/integrations/supabase/client';
import { useAmplifyAuth } from './useAmplifyAuth';
import { User } from '@/types/onboarding';

export function useUserProfile() {
  const { user: authUser } = useAmplifyAuth();
  const [profile, setProfile] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (authUser) {
      fetchProfile();
    } else {
      setLoading(false);
    }
  }, [authUser]);

  const fetchProfile = async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Replace with Amplify GraphQL API
      console.log('TODO: Implement profile fetch with Amplify data layer');

      if (authUser) {
        setProfile({
          id: authUser.userId || '',
          email: authUser.signInDetails?.loginId || '',
          fullName: '',
          role: 'Business Admin',
          companyId: undefined,
          invitedBy: undefined,
          hasCompletedOnboarding: false,
          onboardingStep: 1,
          smsAlerts: true,
          emailAlerts: true,
        });
      }
    } catch (err) {
      console.error('Error fetching profile:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch profile');
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (updates: Partial<User>) => {
    try {
      if (!authUser) throw new Error('User not authenticated');

      // TODO: Replace with Amplify GraphQL API
      console.log('TODO: Implement profile update with Amplify data layer');

      if (profile) {
        setProfile({ ...profile, ...updates });
      }
    } catch (err) {
      console.error('Error updating profile:', err);
      throw err;
    }
  };

  const completeOnboarding = async () => {
    await updateProfile({
      hasCompletedOnboarding: true,
      onboardingStep: 5,
    });
  };

  return {
    profile,
    loading,
    error,
    updateProfile,
    completeOnboarding,
    refetch: fetchProfile,
  };
}
