import { useEffect } from 'react';
import { Authenticator } from '@aws-amplify/ui-react';
import '@aws-amplify/ui-react/styles.css';
import { useNavigate } from 'react-router-dom';
import { useAmplifyAuth } from '@/hooks/useAmplifyAuth';

export default function Auth() {
  const navigate = useNavigate();
  const { user } = useAmplifyAuth();

  useEffect(() => {
    // Redirect to home if user is already authenticated
    if (user) {
      navigate('/');
    }
  }, [user, navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="w-full max-w-md mx-4">
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
            <span className="text-white font-bold text-2xl">C</span>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Welcome to CONVX
          </h1>
          <p className="text-gray-600">
            Sign in to your account or create a new one
          </p>
        </div>

        <Authenticator signUpAttributes={['email']} socialProviders={[]}>
          {({ signOut, user }) => {
            // This will redirect to home page via useEffect when user is authenticated
            return null;
          }}
        </Authenticator>
      </div>
    </div>
  );
}
