{"name": "@convx/auth", "version": "0.0.0", "private": true, "type": "module", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint .", "test": "vitest", "type-check": "tsc --noEmit"}, "dependencies": {"@aws-amplify/ui-react": "^6.11.2", "aws-amplify": "^6.15.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.2"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "jsdom": "^26.0.0", "typescript": "^5.5.3", "vitest": "^2.1.8"}, "peerDependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.2"}}