import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { AuthPage } from '../components/AuthPage';
import { ProtectedRoute } from '../components/ProtectedRoute';

// Mock AWS Amplify
vi.mock('aws-amplify/auth', () => ({
  getCurrentUser: vi.fn(),
  signOut: vi.fn(),
}));

vi.mock('aws-amplify/utils', () => ({
  Hub: {
    listen: vi.fn(() => vi.fn()),
  },
}));

vi.mock('@aws-amplify/ui-react', () => ({
  Authenticator: ({ children }: { children: any }) => (
    <div data-testid="authenticator">
      {typeof children === 'function'
        ? children({ signOut: vi.fn(), user: null })
        : children}
    </div>
  ),
}));

// Mock the auth hook
vi.mock('../hooks/useAmplifyAuth', () => ({
  useAmplifyAuth: () => ({
    user: null,
    loading: false,
    signOut: vi.fn(),
    isAuthenticated: false,
  }),
  AmplifyAuthProvider: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
}));

describe('Auth Components', () => {
  it('renders AuthPage component', () => {
    render(
      <BrowserRouter>
        <AuthPage title="Test Title" subtitle="Test Subtitle" />
      </BrowserRouter>
    );

    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByText('Test Subtitle')).toBeInTheDocument();
  });

  it('renders ProtectedRoute and redirects when not authenticated', () => {
    render(
      <BrowserRouter>
        <ProtectedRoute>
          <div>Protected Content</div>
        </ProtectedRoute>
      </BrowserRouter>
    );

    // Should not show protected content when not authenticated
    expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
  });
});
