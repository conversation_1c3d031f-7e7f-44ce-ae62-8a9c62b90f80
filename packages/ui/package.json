{"name": "@convx/ui", "version": "0.0.0", "private": true, "type": "module", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./styles": "./src/styles.css"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint .", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-slot": "^1.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.462.0", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^2.5.2"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "typescript": "^5.5.3"}, "peerDependencies": {"react": "^18.3.1", "react-dom": "^18.3.1"}}